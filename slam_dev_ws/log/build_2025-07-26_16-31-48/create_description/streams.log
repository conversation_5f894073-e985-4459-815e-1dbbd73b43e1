[0.175s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.603s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.757s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[0.913s] -- Install configuration: ""
[0.925s] -- Execute custom install script
[0.940s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
[0.960s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
[0.981s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
[0.985s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
[0.989s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
[0.990s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
[0.991s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
[0.994s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
[0.995s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
[0.995s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
[0.996s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
[0.997s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
[0.998s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
[0.999s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
[0.999s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
[1.001s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
[1.017s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
[1.031s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
[1.037s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
[1.051s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
[1.052s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
[1.052s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
[1.067s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
[1.212s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
[1.224s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
[1.230s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
[1.242s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
[1.289s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
