[0.096s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.292s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.348s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[0.420s] -- Install configuration: ""
[0.426s] -- Execute custom install script
[0.430s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
[0.435s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
[0.436s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
[0.440s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
[0.445s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
[0.446s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
[0.449s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
[0.452s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
[0.453s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
[0.455s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
[0.456s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
[0.457s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
[0.461s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
[0.462s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
[0.464s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
[0.467s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
[0.469s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
[0.472s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
[0.474s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
[0.478s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
[0.480s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
[0.483s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
[0.485s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
[0.531s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
[0.533s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
[0.537s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
[0.538s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
[0.549s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
