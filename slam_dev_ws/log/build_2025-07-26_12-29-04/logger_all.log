[1.184s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'perceptor']
[1.185s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['perceptor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x75f3d1d85e20>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x75f3d1d85af0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x75f3d1d85af0>>, mixin_verb=('build',))
[1.412s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.413s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.413s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.413s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.413s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.413s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.413s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Roomba/slam_dev_ws'
[1.414s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.576s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.576s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.576s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.576s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.577s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.577s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore'
[1.577s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore_ament_install'
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_pkg']
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_pkg'
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_meta']
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_meta'
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ros']
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ros'
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['cmake', 'python']
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'cmake'
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['python_setup_py']
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python_setup_py'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore_ament_install'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_pkg']
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_pkg'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_meta']
[1.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_meta'
[1.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ros']
[1.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ros'
[1.595s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_bringup' with type 'ros.ament_cmake' and name 'create_bringup'
[1.596s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ignore', 'ignore_ament_install']
[1.596s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore'
[1.596s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore_ament_install'
[1.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_pkg']
[1.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_pkg'
[1.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_meta']
[1.598s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_meta'
[1.598s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ros']
[1.598s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ros'
[1.600s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_description' with type 'ros.ament_cmake' and name 'create_description'
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ignore', 'ignore_ament_install']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore_ament_install'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_pkg']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_pkg'
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_meta']
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_meta'
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ros']
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ros'
[1.615s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_driver' with type 'ros.ament_cmake' and name 'create_driver'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore_ament_install'
[1.617s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_pkg']
[1.617s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_pkg'
[1.617s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_meta']
[1.617s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_meta'
[1.618s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ros']
[1.618s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ros'
[1.620s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_msgs' with type 'ros.ament_cmake' and name 'create_msgs'
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore'
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore_ament_install'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_pkg']
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_pkg'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_meta']
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_meta'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ros']
[1.623s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ros'
[1.625s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_robot' with type 'ros.ament_cmake' and name 'create_robot'
[1.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ignore', 'ignore_ament_install']
[1.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore'
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore_ament_install'
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_pkg']
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_pkg'
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_meta']
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_meta'
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ros']
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ros'
[1.642s] DEBUG:colcon.colcon_core.package_identification:Package 'src/libcreate' with type 'ros.cmake' and name 'libcreate'
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ignore', 'ignore_ament_install']
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore'
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore_ament_install'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_pkg']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_pkg'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_meta']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_meta'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ros']
[1.645s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ros'
[1.648s] DEBUG:colcon.colcon_core.package_identification:Package 'src/perceptor' with type 'ros.ament_cmake' and name 'perceptor'
[1.649s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ignore', 'ignore_ament_install']
[1.649s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore_ament_install'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_pkg']
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_pkg'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_meta']
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_meta'
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ros']
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ros'
[1.653s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rplidar_ros' with type 'ros.ament_cmake' and name 'rplidar_ros'
[1.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.654s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.760s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_description' in 'src/create_robot/create_description'
[1.760s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_msgs' in 'src/create_robot/create_msgs'
[1.760s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'libcreate' in 'src/libcreate'
[1.761s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rplidar_ros' in 'src/rplidar_ros'
[1.761s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_driver' in 'src/create_robot/create_driver'
[1.761s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_bringup' in 'src/create_robot/create_bringup'
[1.761s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_robot' in 'src/create_robot/create_robot'
[1.764s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.765s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.785s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 8 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[1.798s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 370 installed packages in /opt/ros/jazzy
[1.809s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_args' from command line to 'None'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target' from command line to 'None'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_cache' from command line to 'False'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_first' from command line to 'False'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_force_configure' from command line to 'False'
[2.094s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'ament_cmake_args' from command line to 'None'
[2.095s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_cmake_args' from command line to 'None'
[2.095s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.095s] DEBUG:colcon.colcon_core.verb:Building package 'perceptor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/perceptor', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/perceptor', 'symlink_install': False, 'test_result_base': None}
[2.095s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.099s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.102s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor' with build type 'ament_cmake'
[2.103s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor'
[2.137s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.139s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.139s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.216s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[2.459s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[2.520s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[2.731s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[2.736s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[2.772s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[2.778s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[2.783s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[2.786s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[2.791s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[2.794s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[2.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[2.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[2.804s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[2.805s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[2.809s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[2.818s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[2.823s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[2.829s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[2.834s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[2.839s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[2.845s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[2.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[2.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[2.857s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[2.859s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[2.862s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[2.866s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[2.874s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[2.875s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[2.876s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[2.878s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[2.881s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[2.885s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[2.889s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[2.894s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[2.898s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[2.902s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[2.906s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.909s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.910s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.910s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.005s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[3.006s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.006s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[3.110s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.111s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.ps1'
[3.122s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_ps1.py'
[3.143s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.ps1'
[3.165s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.sh'
[3.174s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_sh.py'
[3.185s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.sh'
[3.198s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.bash'
[3.200s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.bash'
[3.208s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.zsh'
[3.211s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.zsh'
