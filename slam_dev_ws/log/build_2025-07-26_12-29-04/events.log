[0.000000] (-) TimerEvent: {}
[0.000831] (-) JobUnselected: {'identifier': 'create_bringup'}
[0.001001] (-) JobUnselected: {'identifier': 'create_description'}
[0.001078] (-) JobUnselected: {'identifier': 'create_driver'}
[0.001148] (-) JobUnselected: {'identifier': 'create_msgs'}
[0.001218] (-) JobUnselected: {'identifier': 'create_robot'}
[0.001289] (-) JobUnselected: {'identifier': 'libcreate'}
[0.001357] (-) JobUnselected: {'identifier': 'rplidar_ros'}
[0.001469] (perceptor) JobQueued: {'identifier': 'perceptor', 'dependencies': OrderedDict({'create_description': '/home/<USER>/Roomba/slam_dev_ws/install/create_description', 'create_msgs': '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs', 'libcreate': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate', 'create_driver': '/home/<USER>/Roomba/slam_dev_ws/install/create_driver', 'create_bringup': '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup'})}
[0.002613] (perceptor) JobStarted: {'identifier': 'perceptor'}
[0.097833] (-) TimerEvent: {}
[0.105814] (perceptor) JobProgress: {'identifier': 'perceptor', 'progress': 'cmake'}
[0.110700] (perceptor) JobProgress: {'identifier': 'perceptor', 'progress': 'build'}
[0.110905] (perceptor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', '--', '-j2', '-l2'], 'cwd': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'smtuser', 'SSH_CLIENT': '************* 50525 22', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/extensions/git/dist/askpass.sh', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/Roomba', 'TERM_PROGRAM_VERSION': '1.102.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1001/vscode-ipc-0af4ab43-f576-40ef-985b-cdaddf73d711.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/node', 'ROS_IP': '*************', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1001/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'COLCON_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'smtuser', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '1139', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/bin/remote-cli:/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts', 'XDG_RUNTIME_DIR': '/run/user/1001', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-00334d92f85b432f.txt', 'LANG': 'en_US.UTF-8', 'LS_COLORS': 'di=01;33:rs=0:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.mi*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1001/vscode-git-5641a0582c.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'QT_QPA_PLATFORM': 'xcb', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '************* 50525 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.198668] (-) TimerEvent: {}
[0.300870] (-) TimerEvent: {}
[0.355962] (perceptor) CommandEnded: {'returncode': 0}
[0.361119] (perceptor) JobProgress: {'identifier': 'perceptor', 'progress': 'install'}
[0.402690] (-) TimerEvent: {}
[0.417777] (perceptor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/Roomba/slam_dev_ws/build/perceptor'], 'cwd': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'smtuser', 'SSH_CLIENT': '************* 50525 22', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/extensions/git/dist/askpass.sh', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/Roomba', 'TERM_PROGRAM_VERSION': '1.102.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1001/vscode-ipc-0af4ab43-f576-40ef-985b-cdaddf73d711.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/extensions/git/dist/askpass-main.js', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/node', 'ROS_IP': '*************', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1001/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'COLCON_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'smtuser', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '1139', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-7adae6a56e34cb64d08899664b814cf620465925/server/bin/remote-cli:/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts', 'XDG_RUNTIME_DIR': '/run/user/1001', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-00334d92f85b432f.txt', 'LANG': 'en_US.UTF-8', 'LS_COLORS': 'di=01;33:rs=0:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.mi*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1001/vscode-git-5641a0582c.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'QT_QPA_PLATFORM': 'xcb', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '************* 50525 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.461661] (perceptor) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.464094] (perceptor) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.468405] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_robot.yaml\n'}
[0.471113] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_sim.yaml\n'}
[0.473762] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/drive_bot.rviz\n'}
[0.474365] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/empty.yaml\n'}
[0.474799] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gaz_ros2_ctl_use_sim.yaml\n'}
[0.477686] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gz_bridge.yaml\n'}
[0.478139] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/joystick.yaml\n'}
[0.480823] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/main.rviz\n'}
[0.482792] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/map.rviz\n'}
[0.483226] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/mapper_params_online_async.yaml\n'}
[0.486047] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/my_controllers.yaml\n'}
[0.486678] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/nav2_params.yaml\n'}
[0.487066] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/twist_mux.yaml\n'}
[0.488649] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/view_bot.rviz\n'}
[0.489292] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/camera.xacro\n'}
[0.493650] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/depth_camera.xacro\n'}
[0.495147] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/face.xacro\n'}
[0.495600] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/gazebo_control.xacro\n'}
[0.495986] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/inertial_macros.xacro\n'}
[0.496345] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/lidar.xacro\n'}
[0.501542] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/robot.urdf.xacro\n'}
[0.502002] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/ros2_control.xacro\n'}
[0.502377] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/ball_tracker.launch.py\n'}
[0.502778] (-) TimerEvent: {}
[0.505627] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/camera.launch.py\n'}
[0.506217] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/joystick.launch.py\n'}
[0.506649] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_robot.launch.py\n'}
[0.507020] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_sim.launch.py\n'}
[0.507377] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/localization_launch.py\n'}
[0.511276] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/navigation_launch.py\n'}
[0.516023] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/online_async_launch.py\n'}
[0.516786] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rplidar.launch.py\n'}
[0.517221] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rsp.launch.py\n'}
[0.517633] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/empty.world\n'}
[0.518008] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/obstacles.world\n'}
[0.518411] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/package_run_dependencies/perceptor\n'}
[0.525265] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/parent_prefix_path/perceptor\n'}
[0.527567] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.sh\n'}
[0.528194] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.dsv\n'}
[0.537616] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.sh\n'}
[0.538274] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.dsv\n'}
[0.538775] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.bash\n'}
[0.543622] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.sh\n'}
[0.544275] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.zsh\n'}
[0.550394] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.dsv\n'}
[0.551161] (perceptor) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv\n'}
[0.603034] (-) TimerEvent: {}
[0.615872] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/packages/perceptor\n'}
[0.618717] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig.cmake\n'}
[0.619392] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig-version.cmake\n'}
[0.622734] (perceptor) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.xml\n'}
[0.632652] (perceptor) CommandEnded: {'returncode': 0}
[0.705399] (-) TimerEvent: {}
[0.805621] (perceptor) JobEnded: {'identifier': 'perceptor', 'rc': 0}
[0.811405] (-) TimerEvent: {}
[0.812872] (-) EventReactorShutdown: {}
