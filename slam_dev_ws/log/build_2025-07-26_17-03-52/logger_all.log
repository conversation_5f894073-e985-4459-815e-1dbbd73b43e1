[1.327s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'perceptor', 'create_description']
[1.327s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['perceptor', 'create_description'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7f7da81822a0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f7da8181c10>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f7da8181c10>>, mixin_verb=('build',))
[1.632s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.634s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.634s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.635s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.635s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.635s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.636s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Roomba/slam_dev_ws'
[1.637s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.637s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.638s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.639s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.640s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.641s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.642s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.642s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.642s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.986s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.986s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.986s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.986s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.986s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.987s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.987s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.988s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.988s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.989s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.989s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.989s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.990s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.990s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.990s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.991s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.991s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.991s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.991s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.992s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.992s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.992s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.992s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.993s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.993s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.993s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.993s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.993s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.994s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.995s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore'
[1.995s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore_ament_install'
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_pkg']
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_pkg'
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_meta']
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_meta'
[1.997s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ros']
[1.997s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ros'
[1.997s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['cmake', 'python']
[1.997s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'cmake'
[1.998s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python'
[1.998s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['python_setup_py']
[1.998s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python_setup_py'
[1.999s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.999s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore'
[2.000s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore_ament_install'
[2.001s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_pkg']
[2.001s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_pkg'
[2.002s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_meta']
[2.002s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_meta'
[2.002s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ros']
[2.002s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ros'
[2.033s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_bringup' with type 'ros.ament_cmake' and name 'create_bringup'
[2.037s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ignore', 'ignore_ament_install']
[2.049s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore'
[2.050s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore_ament_install'
[2.050s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_pkg']
[2.054s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_pkg'
[2.057s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_meta']
[2.061s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_meta'
[2.065s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ros']
[2.067s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ros'
[2.080s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_description' with type 'ros.ament_cmake' and name 'create_description'
[2.081s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ignore', 'ignore_ament_install']
[2.083s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore'
[2.084s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore_ament_install'
[2.085s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_pkg']
[2.085s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_pkg'
[2.086s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_meta']
[2.087s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_meta'
[2.088s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ros']
[2.088s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ros'
[2.143s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_driver' with type 'ros.ament_cmake' and name 'create_driver'
[2.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ignore', 'ignore_ament_install']
[2.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore'
[2.147s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore_ament_install'
[2.147s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_pkg']
[2.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_pkg'
[2.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_meta']
[2.148s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_meta'
[2.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ros']
[2.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ros'
[2.155s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_msgs' with type 'ros.ament_cmake' and name 'create_msgs'
[2.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ignore', 'ignore_ament_install']
[2.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore'
[2.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore_ament_install'
[2.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_pkg']
[2.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_pkg'
[2.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_meta']
[2.166s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_meta'
[2.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ros']
[2.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ros'
[2.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_robot' with type 'ros.ament_cmake' and name 'create_robot'
[2.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ignore', 'ignore_ament_install']
[2.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore'
[2.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore_ament_install'
[2.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_pkg']
[2.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_pkg'
[2.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_meta']
[2.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_meta'
[2.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ros']
[2.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ros'
[2.222s] DEBUG:colcon.colcon_core.package_identification:Package 'src/libcreate' with type 'ros.cmake' and name 'libcreate'
[2.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ignore', 'ignore_ament_install']
[2.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore'
[2.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore_ament_install'
[2.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_pkg']
[2.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_pkg'
[2.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_meta']
[2.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_meta'
[2.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ros']
[2.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ros'
[2.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/perceptor' with type 'ros.ament_cmake' and name 'perceptor'
[2.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ignore', 'ignore_ament_install']
[2.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore'
[2.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore_ament_install'
[2.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_pkg']
[2.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_pkg'
[2.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_meta']
[2.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_meta'
[2.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ros']
[2.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ros'
[2.243s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rplidar_ros' with type 'ros.ament_cmake' and name 'rplidar_ros'
[2.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[2.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[2.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[2.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[2.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[2.384s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_msgs' in 'src/create_robot/create_msgs'
[2.385s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'libcreate' in 'src/libcreate'
[2.387s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rplidar_ros' in 'src/rplidar_ros'
[2.387s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_driver' in 'src/create_robot/create_driver'
[2.387s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_bringup' in 'src/create_robot/create_bringup'
[2.387s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_robot' in 'src/create_robot/create_robot'
[2.395s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[2.395s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[2.421s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 8 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[2.424s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/articubot_one/dev_ws/install
[2.436s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 370 installed packages in /opt/ros/jazzy
[2.452s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.857s] WARNING:colcon.colcon_core.package_selection:Some selected packages are already built in one or more underlay workspaces:
	'create_description' is in: /home/<USER>/Roomba/slam_dev_ws/install/create_description
If a package in a merged underlay workspace is overridden and it installs headers, then all packages in the overlay must sort their include directories by workspace order. Failure to do so may result in build failures or undefined behavior at run time.
If the overridden package is used by another package in any underlay, then the overriding package in the overlay must be API and ABI compatible or undefined behavior at run time may occur.

If you understand the risks and want to override a package anyways, add the following to the command line:
	--allow-overriding create_description

This may be promoted to an error in a future release of colcon-override-check.
[2.863s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_args' from command line to 'None'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target' from command line to 'None'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_cache' from command line to 'False'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_first' from command line to 'False'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_force_configure' from command line to 'False'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'ament_cmake_args' from command line to 'None'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_cmake_args' from command line to 'None'
[2.864s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.865s] DEBUG:colcon.colcon_core.verb:Building package 'create_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_description', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description', 'symlink_install': False, 'test_result_base': None}
[2.866s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_args' from command line to 'None'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target' from command line to 'None'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_cache' from command line to 'False'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_first' from command line to 'False'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_force_configure' from command line to 'False'
[2.867s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'ament_cmake_args' from command line to 'None'
[2.868s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_cmake_args' from command line to 'None'
[2.868s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.869s] DEBUG:colcon.colcon_core.verb:Building package 'perceptor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/perceptor', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/perceptor', 'symlink_install': False, 'test_result_base': None}
[2.869s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.878s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description' with build type 'ament_cmake'
[2.878s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description'
[2.954s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.955s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.955s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.010s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.277s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.327s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.533s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.545s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.588s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.592s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.596s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.598s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.601s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.604s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.609s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.612s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.613s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.617s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.620s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.624s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.628s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.631s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.634s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.636s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.642s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.643s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.646s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.648s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.652s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.652s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.656s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.658s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.661s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.664s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.666s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.668s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.671s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor' with build type 'ament_cmake'
[3.672s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor'
[3.673s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.674s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.787s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.039s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.042s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[4.431s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[4.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[4.437s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[4.439s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[4.444s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[4.445s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[4.449s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[4.452s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[4.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[4.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[4.461s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[4.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[4.465s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[4.470s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[4.474s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[4.479s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[4.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[4.486s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[4.491s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[4.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[4.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[4.504s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[4.506s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[4.509s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[4.513s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[4.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[4.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[4.520s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[4.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[4.524s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[4.530s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[4.535s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[4.538s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[4.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[4.546s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[4.547s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[4.549s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[4.550s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[4.551s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[4.609s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[4.610s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[4.610s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[4.688s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[4.691s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.ps1'
[4.699s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_ps1.py'
[4.714s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.ps1'
[4.739s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.sh'
[4.749s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_sh.py'
[4.763s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.sh'
[4.777s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.bash'
[4.788s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.bash'
[4.814s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.zsh'
[4.820s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.zsh'
