[1.077s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'create_description', 'perceptor', '--allow-overriding', 'create_description']
[1.078s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['create_description', 'perceptor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=['create_description'], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x76a304f760c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x76a304f75b20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x76a304f75b20>>, mixin_verb=('build',))
[1.337s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.338s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.339s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.339s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.340s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Roomba/slam_dev_ws'
[1.341s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.342s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.343s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.343s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.343s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.344s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.344s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.344s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.515s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.515s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.515s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.516s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.517s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.518s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore'
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore_ament_install'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_pkg']
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_pkg'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_meta']
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_meta'
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ros']
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ros'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['cmake', 'python']
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'cmake'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python'
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['python_setup_py']
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python_setup_py'
[1.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.528s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore'
[1.529s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore_ament_install'
[1.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_pkg']
[1.530s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_pkg'
[1.531s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_meta']
[1.531s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_meta'
[1.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ros']
[1.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ros'
[1.546s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_bringup' with type 'ros.ament_cmake' and name 'create_bringup'
[1.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ignore', 'ignore_ament_install']
[1.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore'
[1.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore_ament_install'
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_pkg']
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_pkg'
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_meta']
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_meta'
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ros']
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ros'
[1.554s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_description' with type 'ros.ament_cmake' and name 'create_description'
[1.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ignore', 'ignore_ament_install']
[1.555s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore'
[1.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore_ament_install'
[1.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_pkg']
[1.556s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_pkg'
[1.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_meta']
[1.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_meta'
[1.557s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ros']
[1.558s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ros'
[1.570s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_driver' with type 'ros.ament_cmake' and name 'create_driver'
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore'
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore_ament_install'
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_pkg']
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_pkg'
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_meta']
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_meta'
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ros']
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ros'
[1.577s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_msgs' with type 'ros.ament_cmake' and name 'create_msgs'
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore'
[1.579s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore_ament_install'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_pkg']
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_pkg'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_meta']
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_meta'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ros']
[1.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ros'
[1.585s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_robot' with type 'ros.ament_cmake' and name 'create_robot'
[1.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ignore', 'ignore_ament_install']
[1.586s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore'
[1.586s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore_ament_install'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_pkg']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_pkg'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_meta']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_meta'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ros']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ros'
[1.601s] DEBUG:colcon.colcon_core.package_identification:Package 'src/libcreate' with type 'ros.cmake' and name 'libcreate'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ignore', 'ignore_ament_install']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore'
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore_ament_install'
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_pkg']
[1.603s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_pkg'
[1.604s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_meta']
[1.604s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_meta'
[1.604s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ros']
[1.604s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ros'
[1.608s] DEBUG:colcon.colcon_core.package_identification:Package 'src/perceptor' with type 'ros.ament_cmake' and name 'perceptor'
[1.608s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ignore', 'ignore_ament_install']
[1.609s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore'
[1.609s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore_ament_install'
[1.609s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_pkg']
[1.609s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_pkg'
[1.610s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_meta']
[1.610s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_meta'
[1.610s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ros']
[1.610s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ros'
[1.613s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rplidar_ros' with type 'ros.ament_cmake' and name 'rplidar_ros'
[1.613s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.613s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.613s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.613s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.614s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_msgs' in 'src/create_robot/create_msgs'
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'libcreate' in 'src/libcreate'
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rplidar_ros' in 'src/rplidar_ros'
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_driver' in 'src/create_robot/create_driver'
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_bringup' in 'src/create_robot/create_bringup'
[1.717s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_robot' in 'src/create_robot/create_robot'
[1.721s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.721s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.734s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.735s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.737s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.737s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.738s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.739s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.739s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.739s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.739s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.739s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.740s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.740s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.740s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.753s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 8 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[1.756s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/articubot_one/dev_ws/install
[1.775s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 370 installed packages in /opt/ros/jazzy
[1.788s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.188s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_args' from command line to 'None'
[2.188s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target' from command line to 'None'
[2.188s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.188s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_cache' from command line to 'False'
[2.188s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_first' from command line to 'False'
[2.189s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_force_configure' from command line to 'False'
[2.189s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'ament_cmake_args' from command line to 'None'
[2.189s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_cmake_args' from command line to 'None'
[2.189s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.189s] DEBUG:colcon.colcon_core.verb:Building package 'create_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_description', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description', 'symlink_install': False, 'test_result_base': None}
[2.191s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_args' from command line to 'None'
[2.191s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target' from command line to 'None'
[2.191s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.191s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_cache' from command line to 'False'
[2.192s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_first' from command line to 'False'
[2.192s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_force_configure' from command line to 'False'
[2.192s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'ament_cmake_args' from command line to 'None'
[2.193s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_cmake_args' from command line to 'None'
[2.193s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.193s] DEBUG:colcon.colcon_core.verb:Building package 'perceptor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/perceptor', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/perceptor', 'symlink_install': False, 'test_result_base': None}
[2.195s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.201s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.201s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description' with build type 'ament_cmake'
[2.206s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description'
[2.258s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.259s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.260s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.489s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.167s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.247s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.504s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.515s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.563s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.573s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.583s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.591s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.596s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.602s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.603s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.605s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.607s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.609s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.616s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.621s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.627s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.635s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.644s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.650s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.661s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.662s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.666s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.670s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.676s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.677s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.678s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.679s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.680s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.683s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.686s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.688s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.690s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.693s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.694s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor' with build type 'ament_cmake'
[3.695s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor'
[3.696s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.697s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.870s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.655s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.662s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[5.194s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[5.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[5.225s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[5.237s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[5.253s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[5.260s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[5.266s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[5.276s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[5.289s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[5.301s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[5.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.316s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[5.326s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[5.333s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[5.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[5.347s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[5.355s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[5.362s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[5.363s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[5.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[5.382s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[5.388s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[5.394s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[5.410s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[5.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.428s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[5.434s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[5.437s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[5.450s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[5.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[5.468s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[5.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[5.477s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[5.481s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[5.485s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[5.489s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[5.489s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[5.564s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[5.566s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[5.566s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[5.691s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[5.693s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.ps1'
[5.702s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_ps1.py'
[5.708s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.ps1'
[5.715s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.sh'
[5.720s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_sh.py'
[5.724s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.sh'
[5.732s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.bash'
[5.736s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.bash'
[5.744s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.zsh'
[5.748s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.zsh'
