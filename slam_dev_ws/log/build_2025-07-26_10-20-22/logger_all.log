[1.069s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[1.070s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7f86e90761b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f86e9075c70>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f86e9075c70>>, mixin_verb=('build',))
[1.302s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.304s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Roomba/slam_dev_ws'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.306s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.306s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.306s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.306s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.462s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.462s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.463s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.463s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.463s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.463s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.464s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.464s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.465s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.465s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.466s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.466s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.466s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.467s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.467s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.468s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.468s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.468s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.468s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore'
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore_ament_install'
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_pkg']
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_pkg'
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_meta']
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_meta'
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ros']
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ros'
[1.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['cmake', 'python']
[1.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'cmake'
[1.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python'
[1.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['python_setup_py']
[1.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python_setup_py'
[1.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.475s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore'
[1.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore_ament_install'
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_pkg']
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_pkg'
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_meta']
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_meta'
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ros']
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ros'
[1.492s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_bringup' with type 'ros.ament_cmake' and name 'create_bringup'
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ignore', 'ignore_ament_install']
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore'
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore_ament_install'
[1.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_pkg']
[1.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_pkg'
[1.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_meta']
[1.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_meta'
[1.494s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ros']
[1.495s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ros'
[1.497s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_description' with type 'ros.ament_cmake' and name 'create_description'
[1.498s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ignore', 'ignore_ament_install']
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore'
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore_ament_install'
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_pkg']
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_pkg'
[1.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_meta']
[1.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_meta'
[1.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ros']
[1.500s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ros'
[1.511s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_driver' with type 'ros.ament_cmake' and name 'create_driver'
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore'
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore_ament_install'
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_pkg']
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_pkg'
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_meta']
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_meta'
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ros']
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ros'
[1.517s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_msgs' with type 'ros.ament_cmake' and name 'create_msgs'
[1.517s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore'
[1.518s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore_ament_install'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_pkg']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_pkg'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_meta']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_meta'
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ros']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ros'
[1.522s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_robot' with type 'ros.ament_cmake' and name 'create_robot'
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ignore', 'ignore_ament_install']
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore_ament_install'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_pkg']
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_pkg'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_meta']
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_meta'
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ros']
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ros'
[1.539s] DEBUG:colcon.colcon_core.package_identification:Package 'src/libcreate' with type 'ros.cmake' and name 'libcreate'
[1.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ignore', 'ignore_ament_install']
[1.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore'
[1.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore_ament_install'
[1.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_pkg']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_pkg'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_meta']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_meta'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ros']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ros'
[1.544s] DEBUG:colcon.colcon_core.package_identification:Package 'src/perceptor' with type 'ros.ament_cmake' and name 'perceptor'
[1.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ignore', 'ignore_ament_install']
[1.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore'
[1.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore_ament_install'
[1.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_pkg']
[1.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_pkg'
[1.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_meta']
[1.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_meta'
[1.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ros']
[1.547s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ros'
[1.549s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rplidar_ros' with type 'ros.ament_cmake' and name 'rplidar_ros'
[1.550s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.550s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.550s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.550s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.550s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.650s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.651s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.661s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.662s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.662s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.663s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.663s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.663s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.663s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.663s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.664s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.664s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.664s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.664s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.665s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.665s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.665s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.666s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.666s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/libcreate' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.668s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.668s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.668s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.668s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.668s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.669s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.669s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.669s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.670s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.670s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.670s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/create_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.677s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 0 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[1.679s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/articubot_one/dev_ws/install
[1.694s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 370 installed packages in /opt/ros/jazzy
[1.706s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.086s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_args' from command line to 'None'
[2.087s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target' from command line to 'None'
[2.087s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.087s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_cache' from command line to 'False'
[2.087s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_first' from command line to 'False'
[2.088s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_force_configure' from command line to 'False'
[2.088s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'ament_cmake_args' from command line to 'None'
[2.088s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_cmake_args' from command line to 'None'
[2.088s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.088s] DEBUG:colcon.colcon_core.verb:Building package 'create_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_description', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description', 'symlink_install': True, 'test_result_base': None}
[2.091s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_args' from command line to 'None'
[2.091s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_target' from command line to 'None'
[2.092s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.092s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[2.092s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_clean_first' from command line to 'False'
[2.092s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'cmake_force_configure' from command line to 'False'
[2.093s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'ament_cmake_args' from command line to 'None'
[2.093s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[2.093s] Level 5:colcon.colcon_core.verb:set package 'create_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.093s] DEBUG:colcon.colcon_core.verb:Building package 'create_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_msgs', 'symlink_install': True, 'test_result_base': None}
[2.096s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_args' from command line to 'None'
[2.097s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_target' from command line to 'None'
[2.097s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.097s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_clean_cache' from command line to 'False'
[2.097s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_clean_first' from command line to 'False'
[2.098s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'cmake_force_configure' from command line to 'False'
[2.098s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'ament_cmake_args' from command line to 'None'
[2.099s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'catkin_cmake_args' from command line to 'None'
[2.099s] Level 5:colcon.colcon_core.verb:set package 'libcreate' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.099s] DEBUG:colcon.colcon_core.verb:Building package 'libcreate' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/libcreate', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/libcreate', 'symlink_install': True, 'test_result_base': None}
[2.103s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_args' from command line to 'None'
[2.103s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target' from command line to 'None'
[2.103s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_cache' from command line to 'False'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_first' from command line to 'False'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_force_configure' from command line to 'False'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'ament_cmake_args' from command line to 'None'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_cmake_args' from command line to 'None'
[2.104s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.104s] DEBUG:colcon.colcon_core.verb:Building package 'perceptor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/perceptor', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/perceptor', 'symlink_install': True, 'test_result_base': None}
[2.107s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_args' from command line to 'None'
[2.108s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_target' from command line to 'None'
[2.108s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.108s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_clean_cache' from command line to 'False'
[2.108s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_clean_first' from command line to 'False'
[2.108s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'cmake_force_configure' from command line to 'False'
[2.109s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'ament_cmake_args' from command line to 'None'
[2.109s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'catkin_cmake_args' from command line to 'None'
[2.109s] Level 5:colcon.colcon_core.verb:set package 'rplidar_ros' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.109s] DEBUG:colcon.colcon_core.verb:Building package 'rplidar_ros' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/rplidar_ros', 'symlink_install': True, 'test_result_base': None}
[2.112s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_args' from command line to 'None'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_target' from command line to 'None'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_clean_cache' from command line to 'False'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_clean_first' from command line to 'False'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'cmake_force_configure' from command line to 'False'
[2.114s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'ament_cmake_args' from command line to 'None'
[2.114s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'catkin_cmake_args' from command line to 'None'
[2.114s] Level 5:colcon.colcon_core.verb:set package 'create_driver' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.114s] DEBUG:colcon.colcon_core.verb:Building package 'create_driver' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_driver', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_driver', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_driver', 'symlink_install': True, 'test_result_base': None}
[2.118s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_args' from command line to 'None'
[2.118s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_target' from command line to 'None'
[2.119s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.119s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[2.119s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_clean_first' from command line to 'False'
[2.119s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'cmake_force_configure' from command line to 'False'
[2.119s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'ament_cmake_args' from command line to 'None'
[2.120s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[2.120s] Level 5:colcon.colcon_core.verb:set package 'create_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.120s] DEBUG:colcon.colcon_core.verb:Building package 'create_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_bringup', 'symlink_install': True, 'test_result_base': None}
[2.123s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_args' from command line to 'None'
[2.123s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_target' from command line to 'None'
[2.123s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.123s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_clean_cache' from command line to 'False'
[2.123s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_clean_first' from command line to 'False'
[2.124s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'cmake_force_configure' from command line to 'False'
[2.124s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'ament_cmake_args' from command line to 'None'
[2.124s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'catkin_cmake_args' from command line to 'None'
[2.124s] Level 5:colcon.colcon_core.verb:set package 'create_robot' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.124s] DEBUG:colcon.colcon_core.verb:Building package 'create_robot' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_robot', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_robot', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_robot', 'symlink_install': True, 'test_result_base': None}
[2.125s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.135s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.139s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_msgs' with build type 'ament_cmake'
[2.140s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_msgs'
[2.195s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.196s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.197s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.242s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/libcreate' with build type 'cmake'
[2.245s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/libcreate'
[2.249s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.249s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.425s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs
[2.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/libcreate -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/libcreate
[10.870s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/libcreate -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/libcreate
[10.890s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/libcreate -- -j2 -l2
[65.391s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs
[65.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_msgs -- -j2 -l2
[555.414s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_msgs -- -j2 -l2
[555.640s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_msgs
[601.196s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_msgs)
[601.203s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_msgs
[601.260s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' for CMake module files
[601.269s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' for CMake config files
[601.275s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'cmake_prefix_path')
[601.277s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.ps1'
[601.282s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.dsv'
[601.286s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.sh'
[601.296s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib'
[601.300s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'ld_library_path_lib')
[601.303s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.ps1'
[601.313s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.dsv'
[601.320s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.sh'
[601.343s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/bin'
[601.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/pkgconfig/create_msgs.pc'
[601.362s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages'
[601.365s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'pythonpath')
[601.377s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.ps1'
[601.390s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.dsv'
[601.400s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.sh'
[601.411s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/bin'
[601.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.ps1'
[601.456s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.dsv'
[601.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.sh'
[601.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.bash'
[601.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.zsh'
[601.549s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/colcon-core/packages/create_msgs)
[601.581s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_msgs)
[601.582s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' for CMake module files
[601.612s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs' for CMake config files
[601.633s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'cmake_prefix_path')
[601.634s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.ps1'
[601.661s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.dsv'
[601.666s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/cmake_prefix_path.sh'
[601.683s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib'
[601.687s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'ld_library_path_lib')
[601.688s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.ps1'
[601.699s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.dsv'
[601.705s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/ld_library_path_lib.sh'
[601.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/bin'
[601.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/pkgconfig/create_msgs.pc'
[601.720s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages'
[601.722s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_msgs', 'pythonpath')
[601.724s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.ps1'
[601.728s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.dsv'
[601.733s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/hook/pythonpath.sh'
[601.752s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/bin'
[601.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.ps1'
[601.780s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.dsv'
[601.790s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.sh'
[601.805s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.bash'
[601.810s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/create_msgs/package.zsh'
[601.829s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/share/colcon-core/packages/create_msgs)
[601.853s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description' with build type 'ament_cmake'
[601.853s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description'
[601.854s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[601.889s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[602.215s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_description
[621.288s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_description
[621.337s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[622.327s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[622.347s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[628.381s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[628.401s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[628.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[628.445s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[628.468s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[628.480s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[628.506s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[628.522s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[628.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[628.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[628.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[628.579s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[628.593s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[628.625s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[628.654s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[628.672s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[628.689s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[628.709s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[628.731s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[628.732s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[628.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[628.799s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[628.813s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[628.835s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[628.858s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[628.902s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[628.903s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[628.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[628.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[628.932s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[628.956s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[628.966s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[628.971s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[628.977s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[628.995s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[629.001s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor' with build type 'ament_cmake'
[629.004s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor'
[629.010s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[629.015s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[629.251s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/perceptor -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/perceptor
[649.852s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/perceptor -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/perceptor
[649.883s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[650.979s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[651.018s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[664.889s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[664.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[664.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[664.907s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[664.910s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[664.920s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[664.925s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[664.936s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[664.939s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[664.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[664.937s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[664.960s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[664.979s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[664.998s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[665.018s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[665.028s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[665.039s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[665.051s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[665.088s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[665.111s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[665.141s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[665.160s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[665.168s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[665.196s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[665.223s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[665.264s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[665.266s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[665.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[665.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[665.287s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[665.305s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[665.327s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[665.343s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[665.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[665.389s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[665.418s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/rplidar_ros' with build type 'ament_cmake'
[665.418s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/rplidar_ros'
[665.428s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[665.434s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[665.847s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/rplidar_ros -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros
[704.089s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/rplidar_ros -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros
[704.127s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros -- -j2 -l2
[999.654s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/libcreate -- -j2 -l2
[999.695s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/libcreate
[999.914s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/libcreate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/libcreate
[999.978s] Level 1:colcon.colcon_core.shell:create_environment_hook('libcreate', 'pkg_config_path')
[999.991s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path.ps1'
[1000.008s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path.dsv'
[1000.034s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path.sh'
[1000.080s] Level 1:colcon.colcon_core.shell:create_environment_hook('libcreate', 'pkg_config_path_multiarch')
[1000.090s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path_multiarch.ps1'
[1000.121s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path_multiarch.dsv'
[1000.129s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/pkg_config_path_multiarch.sh'
[1000.143s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(libcreate)
[1000.146s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate' for CMake module files
[1000.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate' for CMake config files
[1000.184s] Level 1:colcon.colcon_core.shell:create_environment_hook('libcreate', 'cmake_prefix_path')
[1000.186s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/cmake_prefix_path.ps1'
[1000.195s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/cmake_prefix_path.dsv'
[1000.202s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/cmake_prefix_path.sh'
[1000.215s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib'
[1000.216s] Level 1:colcon.colcon_core.shell:create_environment_hook('libcreate', 'ld_library_path_lib')
[1000.219s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/ld_library_path_lib.ps1'
[1000.226s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/ld_library_path_lib.dsv'
[1000.233s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/hook/ld_library_path_lib.sh'
[1000.239s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/bin'
[1000.240s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig/libcreate.pc'
[1000.246s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/python3.12/site-packages'
[1000.248s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/bin'
[1000.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/package.ps1'
[1000.284s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/package.dsv'
[1000.301s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/package.sh'
[1000.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/package.bash'
[1000.321s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/libcreate/package.zsh'
[1000.328s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/libcreate/share/colcon-core/packages/libcreate)
[1000.337s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_driver' with build type 'ament_cmake'
[1000.338s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_driver'
[1000.342s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1000.344s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1000.697s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_driver -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_driver
[1049.704s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_driver -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_driver
[1049.728s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_driver -- -j2 -l2
[1515.498s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros -- -j2 -l2
[1515.513s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros
[1521.228s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rplidar_ros)
[1521.230s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' for CMake module files
[1521.239s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' for CMake config files
[1521.241s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/rplidar_ros
[1521.243s] Level 1:colcon.colcon_core.shell:create_environment_hook('rplidar_ros', 'cmake_prefix_path')
[1521.245s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.ps1'
[1521.250s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.dsv'
[1521.253s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.sh'
[1521.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib'
[1521.267s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/bin'
[1521.272s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib/pkgconfig/rplidar_ros.pc'
[1521.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib/python3.12/site-packages'
[1521.299s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/bin'
[1521.303s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.ps1'
[1521.308s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.dsv'
[1521.314s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.sh'
[1521.319s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.bash'
[1521.322s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.zsh'
[1521.327s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/colcon-core/packages/rplidar_ros)
[1521.339s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rplidar_ros)
[1521.342s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' for CMake module files
[1521.353s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros' for CMake config files
[1521.364s] Level 1:colcon.colcon_core.shell:create_environment_hook('rplidar_ros', 'cmake_prefix_path')
[1521.368s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.ps1'
[1521.374s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.dsv'
[1521.381s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/hook/cmake_prefix_path.sh'
[1521.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib'
[1521.398s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/bin'
[1521.398s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib/pkgconfig/rplidar_ros.pc'
[1521.399s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/lib/python3.12/site-packages'
[1521.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/bin'
[1521.413s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.ps1'
[1521.430s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.dsv'
[1521.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.sh'
[1521.449s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.bash'
[1521.454s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/rplidar_ros/package.zsh'
[1521.460s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros/share/colcon-core/packages/rplidar_ros)
[1612.317s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_driver -- -j2 -l2
[1612.324s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_driver
[1613.973s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_driver)
[1613.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' for CMake module files
[1613.982s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_driver' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_driver
[1613.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' for CMake config files
[1613.987s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_driver', 'cmake_prefix_path')
[1613.988s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.ps1'
[1613.990s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.dsv'
[1613.992s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.sh'
[1613.995s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib'
[1613.996s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/bin'
[1613.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib/pkgconfig/create_driver.pc'
[1613.998s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib/python3.12/site-packages'
[1613.999s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/bin'
[1614.000s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.ps1'
[1614.004s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.dsv'
[1614.007s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.sh'
[1614.010s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.bash'
[1614.012s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.zsh'
[1614.013s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/colcon-core/packages/create_driver)
[1614.016s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_driver)
[1614.017s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' for CMake module files
[1614.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver' for CMake config files
[1614.023s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_driver', 'cmake_prefix_path')
[1614.024s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.ps1'
[1614.026s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.dsv'
[1614.028s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/hook/cmake_prefix_path.sh'
[1614.031s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib'
[1614.032s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/bin'
[1614.032s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib/pkgconfig/create_driver.pc'
[1614.033s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/lib/python3.12/site-packages'
[1614.036s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/bin'
[1614.037s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.ps1'
[1614.040s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.dsv'
[1614.043s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.sh'
[1614.046s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.bash'
[1614.050s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/create_driver/package.zsh'
[1614.055s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_driver/share/colcon-core/packages/create_driver)
[1614.064s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_bringup' with build type 'ament_cmake'
[1614.068s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_bringup'
[1614.070s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1614.072s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1614.280s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_bringup -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup
[1627.229s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_bringup -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup
[1627.273s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_bringup -- -j2 -l2
[1627.729s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_bringup -- -j2 -l2
[1627.750s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_bringup
[1631.780s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_bringup)
[1631.786s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' for CMake module files
[1631.790s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_bringup' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_bringup
[1631.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' for CMake config files
[1631.806s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_bringup', 'cmake_prefix_path')
[1631.809s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.ps1'
[1631.822s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.dsv'
[1631.824s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.sh'
[1631.840s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/bin'
[1631.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/lib/pkgconfig/create_bringup.pc'
[1631.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/lib/python3.12/site-packages'
[1631.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/bin'
[1631.879s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.ps1'
[1631.888s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.dsv'
[1631.896s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.sh'
[1631.900s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.bash'
[1631.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.zsh'
[1631.915s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/colcon-core/packages/create_bringup)
[1631.923s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_bringup)
[1631.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' for CMake module files
[1631.935s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup' for CMake config files
[1631.940s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_bringup', 'cmake_prefix_path')
[1631.946s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.ps1'
[1631.953s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.dsv'
[1631.958s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/hook/cmake_prefix_path.sh'
[1631.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/bin'
[1631.971s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/lib/pkgconfig/create_bringup.pc'
[1631.973s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/lib/python3.12/site-packages'
[1631.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/bin'
[1631.992s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.ps1'
[1632.000s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.dsv'
[1632.014s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.sh'
[1632.024s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.bash'
[1632.027s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/create_bringup/package.zsh'
[1632.030s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_bringup/share/colcon-core/packages/create_bringup)
[1632.032s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_robot' with build type 'ament_cmake'
[1632.033s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_robot'
[1632.034s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1632.035s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1632.161s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_robot -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_robot
[1641.608s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake /home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_robot -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/Roomba/slam_dev_ws/install/create_robot
[1641.644s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_robot -- -j2 -l2
[1642.140s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_robot -- -j2 -l2
[1642.153s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_robot
[1644.592s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_robot)
[1644.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' for CMake module files
[1644.598s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_robot' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_robot
[1644.601s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' for CMake config files
[1644.604s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_robot', 'cmake_prefix_path')
[1644.605s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.ps1'
[1644.609s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.dsv'
[1644.614s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.sh'
[1644.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/bin'
[1644.620s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/lib/pkgconfig/create_robot.pc'
[1644.621s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/lib/python3.12/site-packages'
[1644.622s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/bin'
[1644.624s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.ps1'
[1644.628s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.dsv'
[1644.631s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.sh'
[1644.635s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.bash'
[1644.638s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.zsh'
[1644.640s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/colcon-core/packages/create_robot)
[1644.644s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_robot)
[1644.646s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' for CMake module files
[1644.652s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot' for CMake config files
[1644.654s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_robot', 'cmake_prefix_path')
[1644.656s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.ps1'
[1644.659s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.dsv'
[1644.672s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/hook/cmake_prefix_path.sh'
[1644.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/bin'
[1644.687s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/lib/pkgconfig/create_robot.pc'
[1644.688s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/lib/python3.12/site-packages'
[1644.697s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/bin'
[1644.702s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.ps1'
[1644.720s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.dsv'
[1644.729s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.sh'
[1644.737s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.bash'
[1644.744s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.zsh'
[1644.754s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/colcon-core/packages/create_robot)
[1644.758s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1644.761s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1644.762s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1644.762s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1644.862s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1644.864s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1644.864s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1645.023s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1645.028s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.ps1'
[1645.050s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_ps1.py'
[1645.063s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.ps1'
[1645.085s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.sh'
[1645.091s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_sh.py'
[1645.099s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.sh'
[1645.116s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.bash'
[1645.122s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.bash'
[1645.135s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.zsh'
[1645.142s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.zsh'
