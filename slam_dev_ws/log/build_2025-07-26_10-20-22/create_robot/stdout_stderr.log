-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Configuring done (9.3s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/Roomba/slam_dev_ws/build/create_robot
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/ament_index/resource_index/package_run_dependencies/create_robot
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/ament_index/resource_index/parent_prefix_path/create_robot
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/environment/path.sh
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/environment/path.dsv
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/local_setup.bash
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/local_setup.sh
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/local_setup.zsh
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/local_setup.dsv
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.dsv
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/ament_index/resource_index/packages/create_robot
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/cmake/create_robotConfig.cmake
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/cmake/create_robotConfig-version.cmake
-- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_robot/share/create_robot/package.xml
