[0.203s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.869s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.994s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[1.042s] -- Install configuration: ""
[1.055s] -- Execute custom install script
[1.058s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
[1.058s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
[1.062s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
[1.065s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
[1.066s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
[1.066s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
[1.068s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
[1.069s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
[1.073s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
[1.080s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
[1.081s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
[1.081s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
[1.087s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
[1.089s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
[1.096s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
[1.097s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
[1.108s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
[1.113s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
[1.116s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
[1.117s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
[1.117s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
[1.120s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
[1.121s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
[1.168s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
[1.171s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
[1.183s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
[1.192s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
[1.209s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
