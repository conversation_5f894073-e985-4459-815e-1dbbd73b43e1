[1.129s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'create_description', 'perceptor']
[1.130s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['create_description', 'perceptor'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x734f8f4763f0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x734f8f475b20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x734f8f475b20>>, mixin_verb=('build',))
[1.352s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.352s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.352s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.353s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.353s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.353s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.353s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Roomba/slam_dev_ws'
[1.354s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.354s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.515s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.516s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.516s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.517s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.517s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.517s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.518s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.518s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.519s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore'
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ignore_ament_install'
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_pkg']
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_pkg'
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['colcon_meta']
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'colcon_meta'
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['ros']
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'ros'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['cmake', 'python']
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'cmake'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python'
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extensions ['python_setup_py']
[1.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot) by extension 'python_setup_py'
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ignore', 'ignore_ament_install']
[1.525s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ignore_ament_install'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_pkg']
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_pkg'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['colcon_meta']
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'colcon_meta'
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extensions ['ros']
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_bringup) by extension 'ros'
[1.540s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_bringup' with type 'ros.ament_cmake' and name 'create_bringup'
[1.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ignore', 'ignore_ament_install']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ignore_ament_install'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_pkg']
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_pkg'
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['colcon_meta']
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'colcon_meta'
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extensions ['ros']
[1.545s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_description) by extension 'ros'
[1.547s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_description' with type 'ros.ament_cmake' and name 'create_description'
[1.548s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ignore', 'ignore_ament_install']
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore'
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ignore_ament_install'
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_pkg']
[1.549s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_pkg'
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['colcon_meta']
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'colcon_meta'
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extensions ['ros']
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_driver) by extension 'ros'
[1.561s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_driver' with type 'ros.ament_cmake' and name 'create_driver'
[1.562s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.562s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore'
[1.562s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ignore_ament_install'
[1.563s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_pkg']
[1.563s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_pkg'
[1.563s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['colcon_meta']
[1.563s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'colcon_meta'
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extensions ['ros']
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_msgs) by extension 'ros'
[1.566s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_msgs' with type 'ros.ament_cmake' and name 'create_msgs'
[1.567s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ignore', 'ignore_ament_install']
[1.567s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore'
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ignore_ament_install'
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_pkg']
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_pkg'
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['colcon_meta']
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'colcon_meta'
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extensions ['ros']
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(src/create_robot/create_robot) by extension 'ros'
[1.571s] DEBUG:colcon.colcon_core.package_identification:Package 'src/create_robot/create_robot' with type 'ros.ament_cmake' and name 'create_robot'
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ignore', 'ignore_ament_install']
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore'
[1.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ignore_ament_install'
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_pkg']
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_pkg'
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['colcon_meta']
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'colcon_meta'
[1.573s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extensions ['ros']
[1.574s] Level 1:colcon.colcon_core.package_identification:_identify(src/libcreate) by extension 'ros'
[1.587s] DEBUG:colcon.colcon_core.package_identification:Package 'src/libcreate' with type 'ros.cmake' and name 'libcreate'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ignore', 'ignore_ament_install']
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore'
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ignore_ament_install'
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_pkg']
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_pkg'
[1.589s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['colcon_meta']
[1.589s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'colcon_meta'
[1.589s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extensions ['ros']
[1.589s] Level 1:colcon.colcon_core.package_identification:_identify(src/perceptor) by extension 'ros'
[1.592s] DEBUG:colcon.colcon_core.package_identification:Package 'src/perceptor' with type 'ros.ament_cmake' and name 'perceptor'
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ignore', 'ignore_ament_install']
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore'
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ignore_ament_install'
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_pkg']
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_pkg'
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['colcon_meta']
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'colcon_meta'
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extensions ['ros']
[1.595s] Level 1:colcon.colcon_core.package_identification:_identify(src/rplidar_ros) by extension 'ros'
[1.598s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rplidar_ros' with type 'ros.ament_cmake' and name 'rplidar_ros'
[1.598s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.598s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.599s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.599s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.599s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_msgs' in 'src/create_robot/create_msgs'
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'libcreate' in 'src/libcreate'
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rplidar_ros' in 'src/rplidar_ros'
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_driver' in 'src/create_robot/create_driver'
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_bringup' in 'src/create_robot/create_bringup'
[1.695s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'create_robot' in 'src/create_robot/create_robot'
[1.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.699s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.711s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.712s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.712s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.712s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.712s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.715s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.715s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.715s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.716s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.725s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.730s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.732s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.733s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.736s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.737s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.737s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.737s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.779s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 8 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[1.794s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/articubot_one/dev_ws/install
[1.827s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 370 installed packages in /opt/ros/jazzy
[1.872s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.406s] WARNING:colcon.colcon_core.package_selection:Some selected packages are already built in one or more underlay workspaces:
	'create_description' is in: /home/<USER>/Roomba/slam_dev_ws/install/create_description
If a package in a merged underlay workspace is overridden and it installs headers, then all packages in the overlay must sort their include directories by workspace order. Failure to do so may result in build failures or undefined behavior at run time.
If the overridden package is used by another package in any underlay, then the overriding package in the overlay must be API and ABI compatible or undefined behavior at run time may occur.

If you understand the risks and want to override a package anyways, add the following to the command line:
	--allow-overriding create_description

This may be promoted to an error in a future release of colcon-override-check.
[2.420s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_args' from command line to 'None'
[2.420s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target' from command line to 'None'
[2.421s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.421s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_cache' from command line to 'False'
[2.421s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_clean_first' from command line to 'False'
[2.421s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'cmake_force_configure' from command line to 'False'
[2.421s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'ament_cmake_args' from command line to 'None'
[2.423s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_cmake_args' from command line to 'None'
[2.424s] Level 5:colcon.colcon_core.verb:set package 'create_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.424s] DEBUG:colcon.colcon_core.verb:Building package 'create_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/create_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/create_description', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description', 'symlink_install': False, 'test_result_base': None}
[2.428s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_args' from command line to 'None'
[2.428s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target' from command line to 'None'
[2.428s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.430s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_cache' from command line to 'False'
[2.431s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_clean_first' from command line to 'False'
[2.431s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'cmake_force_configure' from command line to 'False'
[2.431s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'ament_cmake_args' from command line to 'None'
[2.431s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_cmake_args' from command line to 'None'
[2.431s] Level 5:colcon.colcon_core.verb:set package 'perceptor' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.432s] DEBUG:colcon.colcon_core.verb:Building package 'perceptor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Roomba/slam_dev_ws/build/perceptor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Roomba/slam_dev_ws/install/perceptor', 'merge_install': False, 'path': '/home/<USER>/Roomba/slam_dev_ws/src/perceptor', 'symlink_install': False, 'test_result_base': None}
[2.432s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.445s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.448s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description' with build type 'ament_cmake'
[2.451s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/create_robot/create_description'
[2.539s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.543s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.546s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.653s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.318s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[3.445s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.653s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.658s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[3.731s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.743s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.745s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.750s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.753s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.772s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.776s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.781s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.787s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.793s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.799s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(create_description)
[3.809s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake module files
[3.834s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description' for CMake config files
[3.845s] Level 1:colcon.colcon_core.shell:create_environment_hook('create_description', 'cmake_prefix_path')
[3.850s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.ps1'
[3.860s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.dsv'
[3.873s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/hook/cmake_prefix_path.sh'
[3.900s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.904s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/pkgconfig/create_description.pc'
[3.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/lib/python3.12/site-packages'
[3.912s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/create_description/bin'
[3.915s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.ps1'
[3.927s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv'
[3.934s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.sh'
[3.957s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.bash'
[3.966s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.zsh'
[3.980s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/colcon-core/packages/create_description)
[3.989s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor' with build type 'ament_cmake'
[3.993s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Roomba/slam_dev_ws/src/perceptor'
[3.995s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.996s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[4.334s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.766s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[4.773s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[5.153s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[5.155s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[5.163s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[5.166s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[5.177s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[5.179s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[5.186s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[5.191s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[5.194s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.195s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[5.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[5.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.199s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[5.202s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[5.204s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[5.207s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[5.209s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[5.211s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[5.214s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(perceptor)
[5.215s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake module files
[5.218s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor' for CMake config files
[5.220s] Level 1:colcon.colcon_core.shell:create_environment_hook('perceptor', 'cmake_prefix_path')
[5.221s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.ps1'
[5.223s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.dsv'
[5.225s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/hook/cmake_prefix_path.sh'
[5.229s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.230s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/pkgconfig/perceptor.pc'
[5.231s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/lib/python3.12/site-packages'
[5.232s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/bin'
[5.234s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.ps1'
[5.236s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv'
[5.239s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.sh'
[5.243s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.bash'
[5.246s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.zsh'
[5.249s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/colcon-core/packages/perceptor)
[5.251s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[5.253s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[5.254s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[5.254s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[5.342s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[5.345s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[5.349s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[5.490s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[5.498s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.ps1'
[5.518s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_ps1.py'
[5.531s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.ps1'
[5.541s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.sh'
[5.544s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Roomba/slam_dev_ws/install/_local_setup_util_sh.py'
[5.548s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.sh'
[5.557s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.bash'
[5.560s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.bash'
[5.569s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Roomba/slam_dev_ws/install/local_setup.zsh'
[5.572s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Roomba/slam_dev_ws/install/setup.zsh'
