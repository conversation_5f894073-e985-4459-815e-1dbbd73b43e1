[0.160s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.432s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.514s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[0.632s] -- Install configuration: ""
[0.637s] -- Execute custom install script
[0.664s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
[0.665s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
[0.665s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
[0.672s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
[0.675s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
[0.677s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
[0.681s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
[0.683s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
[0.686s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
[0.687s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
[0.694s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
[0.696s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
[0.702s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
[0.704s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
[0.707s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
[0.709s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
[0.713s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
[0.714s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
[0.718s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
[0.719s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
[0.722s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
[0.724s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
[0.726s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
[0.777s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
[0.778s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
[0.781s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
[0.782s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
[0.796s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
