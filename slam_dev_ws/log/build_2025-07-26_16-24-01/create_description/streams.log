[0.162s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.478s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/create_description -- -j2 -l2
[0.646s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
[0.725s] -- Install configuration: ""
[0.726s] -- Execute custom install script
[0.737s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
[0.737s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
[0.738s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
[0.744s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
[0.746s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
[0.746s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
[0.752s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
[0.756s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
[0.762s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
[0.763s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
[0.768s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
[0.768s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
[0.772s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
[0.772s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
[0.773s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
[0.778s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
[0.780s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
[0.781s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
[0.786s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
[0.790s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
[0.797s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
[0.801s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
[0.811s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
[0.977s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
[0.984s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
[0.990s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
[0.997s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
[1.014s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/create_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/create_description
