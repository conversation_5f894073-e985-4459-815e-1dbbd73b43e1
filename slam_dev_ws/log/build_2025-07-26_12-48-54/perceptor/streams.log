[0.218s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[0.895s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[0.917s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[1.021s] -- Install configuration: ""
[1.030s] -- Execute custom install script
[1.041s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_robot.yaml
[1.052s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_sim.yaml
[1.058s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/drive_bot.rviz
[1.070s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/empty.yaml
[1.073s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gaz_ros2_ctl_use_sim.yaml
[1.078s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gz_bridge.yaml
[1.085s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/joystick.yaml
[1.089s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/main.rviz
[1.091s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/map.rviz
[1.092s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/mapper_params_online_async.yaml
[1.101s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/my_controllers.yaml
[1.110s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/nav2_params.yaml
[1.115s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/twist_mux.yaml
[1.118s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/view_bot.rviz
[1.134s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/camera.xacro
[1.139s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/depth_camera.xacro
[1.142s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/face.xacro
[1.149s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/gazebo_control.xacro
[1.157s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/inertial_macros.xacro
[1.160s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/lidar.xacro
[1.165s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/robot.urdf.xacro
[1.174s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/ros2_control.xacro
[1.178s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/ball_tracker.launch.py
[1.183s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/camera.launch.py
[1.187s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/joystick.launch.py
[1.192s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_robot.launch.py
[1.197s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_sim.launch.py
[1.204s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/localization_launch.py
[1.205s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/navigation_launch.py
[1.209s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/online_async_launch.py
[1.210s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rplidar.launch.py
[1.217s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rsp.launch.py
[1.225s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/empty.world
[1.230s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/obstacles.world
[1.241s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/package_run_dependencies/perceptor
[1.247s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/parent_prefix_path/perceptor
[1.252s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.sh
[1.264s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.dsv
[1.283s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.sh
[1.301s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.dsv
[1.302s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.bash
[1.310s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.sh
[1.323s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.zsh
[1.329s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.dsv
[1.332s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv
[1.545s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/packages/perceptor
[1.562s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig.cmake
[1.568s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig-version.cmake
[1.570s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.xml
[1.610s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
