[0.298s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[0.654s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/Roomba/slam_dev_ws/build/perceptor -- -j2 -l2
[0.658s] Invoking command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
[0.703s] -- Install configuration: ""
[0.705s] -- Execute custom install script
[0.710s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_robot.yaml
[0.712s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/ball_tracker_params_sim.yaml
[0.713s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/drive_bot.rviz
[0.713s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/empty.yaml
[0.714s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gaz_ros2_ctl_use_sim.yaml
[0.714s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/gz_bridge.yaml
[0.721s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/joystick.yaml
[0.729s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/main.rviz
[0.730s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/map.rviz
[0.730s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/mapper_params_online_async.yaml
[0.731s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/my_controllers.yaml
[0.731s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/nav2_params.yaml
[0.738s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/twist_mux.yaml
[0.742s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/config/view_bot.rviz
[0.744s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/camera.xacro
[0.746s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/depth_camera.xacro
[0.747s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/face.xacro
[0.748s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/gazebo_control.xacro
[0.748s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/inertial_macros.xacro
[0.754s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/lidar.xacro
[0.756s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/robot.urdf.xacro
[0.764s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/description/ros2_control.xacro
[0.767s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/ball_tracker.launch.py
[0.771s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/camera.launch.py
[0.774s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/joystick.launch.py
[0.776s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_robot.launch.py
[0.781s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/launch_sim.launch.py
[0.785s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/localization_launch.py
[0.792s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/navigation_launch.py
[0.795s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/online_async_launch.py
[0.797s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rplidar.launch.py
[0.798s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/launch/rsp.launch.py
[0.798s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/empty.world
[0.804s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/worlds/obstacles.world
[0.809s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/package_run_dependencies/perceptor
[0.809s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/parent_prefix_path/perceptor
[0.810s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.sh
[0.811s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/ament_prefix_path.dsv
[0.812s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.sh
[0.812s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/environment/path.dsv
[0.813s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.bash
[0.814s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.sh
[0.815s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.zsh
[0.815s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/local_setup.dsv
[0.817s] -- Symlinking: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.dsv
[0.817s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/ament_index/resource_index/packages/perceptor
[0.818s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig.cmake
[0.818s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/cmake/perceptorConfig-version.cmake
[0.818s] -- Up-to-date symlink: /home/<USER>/Roomba/slam_dev_ws/install/perceptor/share/perceptor/package.xml
[0.820s] Invoked command in '/home/<USER>/Roomba/slam_dev_ws/build/perceptor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/home/<USER>/articubot_one/dev_ws/install/articubot_one:/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/perceptor:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/Roomba/slam_dev_ws/build/perceptor
