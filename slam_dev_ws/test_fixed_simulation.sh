#!/bin/bash

# Test script for fixed Perceptor simulation
cd "$(dirname "$0")"
source install/setup.bash

echo "🧪 Testing Fixed Perceptor Simulation"
echo "====================================="

echo ""
echo "1. Testing URDF generation..."
if xacro src/perceptor/description/robot.urdf.xacro use_ros2_control:=true use_sim_time:=true > /tmp/test_urdf.urdf 2>/dev/null; then
    echo "✅ URDF generated successfully"
    
    # Check for materials
    material_count=$(grep -c "material name" /tmp/test_urdf.urdf)
    echo "✅ Found $material_count material definitions"
    
    # Check for ros2_control
    if grep -q "ros2_control" /tmp/test_urdf.urdf; then
        echo "✅ ros2_control configuration included"
    else
        echo "❌ ros2_control configuration missing"
    fi
    
    # Check for inertial properties
    if grep -q "base_footprint" /tmp/test_urdf.urdf && grep -A 10 "base_footprint" /tmp/test_urdf.urdf | grep -q "inertial"; then
        echo "✅ base_footprint has inertial properties"
    else
        echo "❌ base_footprint missing inertial properties"
    fi
else
    echo "❌ URDF generation failed"
    exit 1
fi

echo ""
echo "2. Expected improvements:"
echo "✅ No more 'material undefined' warnings"
echo "✅ No more 'invalid inertia' errors"
echo "✅ Controllers should start successfully"
echo "✅ Robot should respond to movement commands"

echo ""
echo "3. To test the simulation:"
echo "   Terminal 1: ros2 launch perceptor launch_sim.launch.py"
echo "   Terminal 2: ros2 run rviz2 rviz2 -d src/perceptor/config/main.rviz"
echo "   Terminal 3: ros2 topic pub --once /diff_cont/cmd_vel_unstamped geometry_msgs/msg/Twist '{linear: {x: 0.2}}'"

echo ""
echo "4. Expected clean startup messages:"
echo "   [INFO] [robot_state_publisher]: Robot initialized"
echo "   [INFO] [spawner_diff_cont]: Successfully loaded controller diff_cont"
echo "   [INFO] [spawner_joint_broad]: Successfully loaded controller joint_broad"

echo ""
echo "🎯 Test completed! The simulation should now work without errors."
