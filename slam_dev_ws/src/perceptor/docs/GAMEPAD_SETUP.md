# Bluetooth Gamepad Setup for Pi 5

This guide explains how to set up a Bluetooth gamepad for controlling the Perceptor robot on Raspberry Pi 5.

## Supported Controllers

- **Xbox One/Series Controllers** (Recommended)
- **PlayStation 4/5 Controllers** 
- **Generic Bluetooth gamepads**
- **Logitech F710** (with USB dongle)

## Pi 5 Bluetooth Setup

### 1. Enable Bluetooth
```bash
# Check if Bluetooth is enabled
sudo systemctl status bluetooth

# Enable if needed
sudo systemctl enable bluetooth
sudo systemctl start bluetooth
```

### 2. Pair Your Controller

#### Xbox Controller:
```bash
# Put controller in pairing mode (hold Xbox + Connect buttons)
sudo bluetoothctl
scan on
# Look for "Xbox Wireless Controller"
pair XX:XX:XX:XX:XX:XX
trust XX:XX:XX:XX:XX:XX
connect XX:XX:XX:XX:XX:XX
exit
```

#### PlayStation Controller:
```bash
# Put controller in pairing mode (hold Share + PS buttons)
sudo bluetoothctl
scan on
# Look for "Wireless Controller"
pair XX:XX:XX:XX:XX:XX
trust XX:XX:XX:XX:XX:XX
connect XX:XX:XX:XX:XX:XX
exit
```

### 3. Test Controller Connection
```bash
# Check if controller is detected
ls /dev/input/js*
# Should show: /dev/input/js0

# Test joystick input
ros2 run joy joy_node
# In another terminal:
ros2 topic echo /joy
```

## Robot Control

### Start Robot with Gamepad
```bash
# Full robot with gamepad (default)
ros2 launch perceptor launch_robot.launch.py

# Robot without gamepad
ros2 launch perceptor launch_robot.launch.py enable_joystick:=false
```

### Control Mapping (Default Xbox Layout)
- **Left Stick**: Forward/backward movement
- **Right Stick**: Rotation (left/right)
- **Right Bumper (RB)**: Enable button (must hold to move)
- **Left Bumper (LB)**: Turbo mode (faster movement)

### Troubleshooting

#### Controller Not Detected:
```bash
# Check Bluetooth status
hciconfig
sudo hciconfig hci0 up

# Restart Bluetooth service
sudo systemctl restart bluetooth
```

#### Permission Issues:
```bash
# Add user to input group
sudo usermod -a -G input $USER
# Logout and login again
```

#### Connection Drops:
```bash
# Increase Bluetooth power
echo 'options bluetooth disable_ertm=1' | sudo tee -a /etc/modprobe.d/bluetooth.conf
sudo reboot
```

## Configuration Files

Gamepad settings are in: `config/joystick.yaml`

You can modify:
- Button mappings
- Axis assignments  
- Speed scaling
- Deadzone settings

## Testing Without Hardware

```bash
# Simulate joystick input for testing
ros2 topic pub /joy sensor_msgs/Joy "
header:
  stamp: {sec: 0, nanosec: 0}
  frame_id: ''
axes: [0.0, 0.5, 0.0, 0.0, 0.0, 0.0]
buttons: [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
"
```

This will make the robot move forward slowly with the enable button "pressed".
