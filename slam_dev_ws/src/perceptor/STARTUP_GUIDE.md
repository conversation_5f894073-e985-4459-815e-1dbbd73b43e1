# 🚀 Perceptor Robot Startup Guide

Complete step-by-step guide for bringing up the Perceptor robot with integrated Create base in both simulation and real robot modes.

## 🎮 Simulation Mode Startup

### **Step 1: Launch Simulation Environment**
```bash
# Terminal 1: Launch complete simulation
cd ~/Roomba/slam_dev_ws
source install/setup.bash
ros2 launch perceptor launch_sim.launch.py
```

**⏱️ Timing**: Wait 10-15 seconds for complete initialization

**✅ Expected Output**:
```
[INFO] [robot_state_publisher]: Robot description loaded
[INFO] [gazebo]: Gazebo started
[INFO] [spawn_entity.py]: Entity spawned successfully
[INFO] [controller_manager]: diff_cont controller started
[INFO] [controller_manager]: joint_broad controller started
```

**🔍 Verification Commands**:
```bash
# Terminal 2: Check simulation topics
ros2 topic list | grep -E "(cmd_vel|scan|odom|joint_states)"

# Expected output:
# /diff_cont/cmd_vel_unstamped
# /scan
# /odom  
# /joint_states
# /robot_description
```

**🚨 Troubleshooting**:
- **Gazebo doesn't start**: Check if another Gazebo instance is running: `pkill -f gazebo`
- **Robot not visible**: Check entity spawning logs for URDF errors
- **Controllers fail**: Verify ros2_control configuration in `my_controllers.yaml`

### **Step 2: Launch RViz Visualization**
```bash
# Terminal 3: Launch RViz with main configuration
ros2 run rviz2 rviz2 -d src/perceptor/config/main.rviz
```

**✅ Expected RViz Display**:
- **Robot Model**: White cylinder (base_link) with black LiDAR cylinder on top
- **LiDAR Scan**: Colorful points showing environment obstacles
- **Camera Feed**: Simulation camera view in separate panel
- **TF Frames**: Coordinate frames properly connected (odom → base_link → sensors)
- **Grid**: Reference grid for scale and positioning

**🔍 RViz Verification Checklist**:
- [ ] Fixed Frame: `odom` (for navigation) or `base_link` (for robot-centric view)
- [ ] Robot model loads without errors
- [ ] LiDAR points appear and update in real-time
- [ ] Camera image shows simulation environment
- [ ] No red error messages in Displays panel

### **Step 3: Test Robot Movement**
```bash
# Terminal 4: Test forward movement
ros2 topic pub --once /diff_cont/cmd_vel_unstamped geometry_msgs/msg/Twist '{linear: {x: 0.2}}'

# Test turning
ros2 topic pub --once /diff_cont/cmd_vel_unstamped geometry_msgs/msg/Twist '{angular: {z: 0.5}}'

# Stop robot
ros2 topic pub --once /diff_cont/cmd_vel_unstamped geometry_msgs/msg/Twist '{}'
```

**✅ Expected Behavior**:
- Robot moves forward/turns in Gazebo
- Odometry updates in RViz (robot position changes)
- LiDAR scan data changes as robot moves
- Joint states update (wheel rotations visible)

### **Step 4: Enable Joystick Control (Optional)**
```bash
# Terminal 5: Launch joystick control
ros2 launch perceptor joystick.launch.py use_sim_time:=true
```

**🎮 Nintendo Pro Controller**:
- **Y Button**: Hold for deadman switch
- **Left Stick**: Up/Down = forward/backward, Left/Right = turn

---

## 🔧 Real Robot Mode Startup

### **Step 1: Connect to Physical Robot**
```bash
# Check USB connection to Create robot
ls /dev/ttyUSB* 

# Should show: /dev/ttyUSB0 (or similar)
```

### **Step 2: Launch Real Robot System**
```bash
# Terminal 1: Launch real robot
cd ~/Roomba/slam_dev_ws
source install/setup.bash
ros2 launch perceptor launch_robot.launch.py
```

**⏱️ Timing**: Wait 5-10 seconds for create_driver initialization

**✅ Expected Output**:
```
[INFO] [create_driver]: Connected to Create robot
[INFO] [robot_state_publisher]: Robot description loaded
[INFO] [twist_mux]: Twist multiplexer started
```

**🔍 Verification Commands**:
```bash
# Terminal 2: Check real robot topics
ros2 topic list | grep -E "(cmd_vel|scan|odom|joint_states)"

# Expected output:
# /cmd_vel
# /scan
# /odom
# /joint_states
```

### **Step 3: Launch RViz for Real Robot**
```bash
# Terminal 3: Launch RViz
ros2 run rviz2 rviz2 -d src/perceptor/config/main.rviz
```

**✅ Expected RViz Display**:
- **Robot Model**: Same as simulation (white cylinder with sensors)
- **LiDAR Scan**: Real sensor data from physical environment
- **Camera Feed**: Real camera images
- **Odometry**: Updates from Create robot encoders

### **Step 4: Test Real Robot Movement**
```bash
# Terminal 4: Test movement (CAUTION: Robot will move!)
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{linear: {x: 0.1}}'
```

**⚠️ Safety Notes**:
- Ensure robot has clear path
- Start with low velocities (0.1 m/s)
- Have emergency stop ready (Ctrl+C)

---

## 📊 RViz Configuration Details

### **Available RViz Configurations**:

1. **`main.rviz`**: Complete visualization with robot, LiDAR, and camera
   - Best for: General development and testing
   - Fixed Frame: `odom`
   - Displays: RobotModel, LaserScan, Camera, TF

2. **`view_bot.rviz`**: Robot-focused view for teleoperation
   - Best for: Manual control and robot inspection
   - Fixed Frame: `base_link`
   - Displays: RobotModel, TF (detailed)

3. **`drive_bot.rviz`**: Driving-focused interface
   - Best for: Navigation and path planning
   - Fixed Frame: `odom`
   - Displays: RobotModel, Grid, Navigation tools

4. **`map.rviz`**: SLAM and mapping visualization
   - Best for: SLAM, localization, and mapping
   - Fixed Frame: `map`
   - Displays: Map, LaserScan, RobotModel, TF

### **Sensor Frame Positions** (Verified in URDF):
```
base_link (Create robot center)
├── laser (0, 0, 0.2)           # LiDAR: 200mm above center
└── camera_link (0.075, 0, 0.1) # Camera: 75mm forward, 100mm up
    └── camera_link_optical      # Standard camera optical frame
```

---

## 🔧 Troubleshooting Guide

### **Common Issues & Solutions**:

#### **Simulation Issues**:
- **Robot not spawning**: Check URDF syntax with `xacro` command
- **Controllers not starting**: Verify `my_controllers.yaml` configuration
- **LiDAR not working**: Check Gazebo sensor plugin loading
- **Camera not working**: Verify camera plugin in URDF

#### **Real Robot Issues**:
- **create_driver fails**: Check USB connection and permissions
- **No sensor data**: Verify physical sensor connections
- **Robot doesn't move**: Check create_driver status and battery level
- **Odometry drift**: Calibrate Create robot wheel parameters

#### **RViz Issues**:
- **Robot model not loading**: Check `/robot_description` topic
- **TF errors**: Verify all frame transforms are publishing
- **LiDAR not visible**: Check `/scan` topic and frame_id
- **Camera not showing**: Verify `/camera/image_raw` topic

### **Diagnostic Commands**:
```bash
# Check all active topics
ros2 topic list

# Monitor specific topic
ros2 topic echo /scan --once

# Check TF tree
ros2 run tf2_tools view_frames

# Verify robot description
ros2 param get /robot_state_publisher robot_description

# Check node status
ros2 node list
```

---

## 🎯 Next Steps

After successful startup, you can proceed with:

1. **Navigation Setup**: Configure Nav2 for autonomous navigation
2. **SLAM**: Use slam_toolbox for mapping
3. **Autonomous Behaviors**: Implement custom behaviors using sensor data
4. **Multi-Robot**: Scale to multiple Perceptor robots

**Happy Robot Development!** 🤖✨
