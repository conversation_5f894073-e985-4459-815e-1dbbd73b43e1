<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" >

    <!-- Camera mounted 75mm forward, 100mm above base_link -->
    <joint name="camera_joint" type="fixed">
        <parent link="base_link"/>
        <child link="camera_link"/>
        <origin xyz="0.075 0 0.1" rpy="0 0 0"/>
    </joint>

    <link name="camera_link">
        <inertial>
            <mass value="0.01" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.000001" ixy="0.0" ixz="0.0"
                     iyy="0.000001" iyz="0.0"
                     izz="0.000001" />
        </inertial>
        <visual>
            <geometry>
                <box size="0.010 0.03 0.03"/>
            </geometry>
            <material name="black"/>
        </visual>
        <visual>
            <origin xyz="0 0 -0.05"/>
            <geometry>
                <cylinder radius="0.002" length="0.1"/>
            </geometry>
            <material name="black"/>
        </visual>
        <collision>
            <geometry>
                <box size="0.010 0.03 0.03"/>
            </geometry>
        </collision>
    </link>


    <joint name="camera_optical_joint" type="fixed">
        <parent link="camera_link"/>
        <child link="camera_link_optical"/>
        <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}"/>
    </joint>

    <link name="camera_link_optical"></link>

    <gazebo reference="camera_link">
        <material>Gazebo/Black</material>

        <sensor name="camera" type="camera">
            <pose> 0 0 0 0 0 0 </pose>
            <visualize>true</visualize>
            <update_rate>10</update_rate>
            <camera>
                <camera_info_topic>camera/camera_info</camera_info_topic>
                <horizontal_fov>1.089</horizontal_fov>
                <image>
                    <format>R8G8B8</format>
                    <width>640</width>
                    <height>480</height>
                </image>
                <clip>
                    <near>0.05</near>
                    <far>8.0</far>
                </clip>
            </camera>
            <topic>camera/image_raw</topic>
            <gz_frame_id>camera_link_optical</gz_frame_id>
        </sensor>
    </gazebo>

</robot>