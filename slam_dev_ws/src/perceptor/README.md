# Perceptor Robot Package

The Perceptor robot package integrates the Create 2 robot base with LiDAR and camera sensors for autonomous navigation and perception tasks. This package supports both simulation and real robot operation with clearly differentiated control architectures.

## 🤖 Robot Architecture

### Hardware Configuration
- **Base Platform**: iRobot Create 2 robot base
  - Wheel separation: 0.235m
  - Wheel radius: 0.036m
  - Mass: 2.0kg
  - Differential drive system

- **Sensors**:
  - **LiDAR**: Mounted at geometric center, 200mm above base_link
    - 360° scanning capability
    - Range: 0.15m to 12m
    - Update rate: 10Hz
  - **Camera**: Mounted 75mm forward, 100mm above base_link
    - Resolution: 640x480
    - Field of view: 62.4° horizontal
    - Update rate: 10Hz

### Software Architecture

The Perceptor package supports two distinct operational modes:

#### 🎮 Simulation Mode (`use_sim_time=true`)
- **Control System**: Differential drive plugin + ros2_control framework
- **Physics**: Gazebo simulation with realistic Create robot specifications
- **Topics**: Standard ros2_control topics
  - `/diff_cont/cmd_vel_unstamped` - Velocity commands to controller
  - `/joint_states` - Joint positions and velocities
  - `/odom` - Odometry from differential drive plugin

#### 🔧 Real Robot Mode (`use_sim_time=false`)
- **Control System**: create_driver topics from Create robot hardware
- **Hardware**: Physical Create robot motors, encoders, and sensors
- **Topics**: create_driver native topics
  - `/cmd_vel` - Direct velocity commands to Create robot
  - `/odom` - Odometry from Create robot encoders
  - `/joint_states` - Joint states from Create robot hardware

## 📋 Topic Mappings

### Simulation Mode Topics
```
Input Topics:
├── /cmd_vel_teleop          → Joystick commands (Nintendo Pro Controller)
├── /cmd_vel_nav             → Navigation commands
└── /cmd_vel_auto            → Autonomous behavior commands

Internal Topics:
├── /cmd_vel_out             → Twist multiplexer output
├── /diff_cont/cmd_vel_unstamped → Differential drive controller input
├── /joint_states            → Joint positions/velocities (ros2_control)
└── /odom                    → Odometry (differential drive plugin)

Sensor Topics:
├── /scan                    → LiDAR data (sensor_msgs/LaserScan)
├── /camera/image_raw        → Camera images (sensor_msgs/Image)
└── /camera/camera_info      → Camera calibration (sensor_msgs/CameraInfo)
```

### Real Robot Mode Topics
```
Input Topics:
├── /cmd_vel_teleop          → Joystick commands (Nintendo Pro Controller)
├── /cmd_vel_nav             → Navigation commands
└── /cmd_vel_auto            → Autonomous behavior commands

Internal Topics:
├── /cmd_vel_out             → Twist multiplexer output
├── /cmd_vel                 → Direct Create robot commands
├── /joint_states            → Joint positions/velocities (create_driver)
└── /odom                    → Odometry (Create robot encoders)

Sensor Topics:
├── /scan                    → LiDAR data (physical sensor)
├── /camera/image_raw        → Camera images (physical camera)
└── /camera/camera_info      → Camera calibration (physical camera)
```

## 🚀 Launch Files

### Simulation Launch
```bash
# Launch complete simulation environment
ros2 launch perceptor launch_sim.launch.py

# Launch with specific world
ros2 launch perceptor launch_sim.launch.py world:=/path/to/world.world
```

**Components launched:**
- Robot State Publisher (Create robot model)
- Gazebo simulation environment
- Differential drive controller (ros2_control)
- Joint state broadcaster
- Twist multiplexer
- Nintendo Pro Controller support
- ROS-Gazebo bridges

### Real Robot Launch
```bash
# Launch real robot system
ros2 launch perceptor launch_robot.launch.py
```

**Components launched:**
- Robot State Publisher (Create robot model)
- create_driver (hardware interface)
- Twist multiplexer
- Topic remapping for create_driver integration

## 🎮 Controller Configuration

The Perceptor robot uses the Nintendo Pro Controller for manual teleoperation:

### Button Mapping
- **Y Button**: Deadman switch (must be held for movement)
- **Left Stick**:
  - Up/Down: Forward/backward movement
  - Left/Right: Turning movement

### Speed Settings
- **Slow Mode**: Linear 0.2 m/s, Angular 1.2 rad/s
- **Fast Mode**: Linear 0.4 m/s, Angular 2.4 rad/s

### Usage
```bash
# Enable joystick control (uncomment in launch files)
ros2 launch perceptor joystick.launch.py use_sim_time:=true   # Simulation
ros2 launch perceptor joystick.launch.py use_sim_time:=false  # Real robot
```

## 🔧 Dependencies

### ROS 2 Packages
- `create_description` - Create robot URDF and physical specifications
- `create_bringup` - Create robot launch configurations
- `create_driver` - Create robot hardware interface
- `create_msgs` - Create robot message definitions

### System Requirements
- ROS 2 Jazzy
- Gazebo Harmonic (for simulation)
- Nintendo Pro Controller (for teleoperation)
- Physical Create 2 robot (for real robot mode)

## 📁 Package Structure

```
perceptor/
├── config/                  # Configuration files
│   ├── joystick.yaml       # Nintendo Pro Controller mapping
│   ├── twist_mux.yaml      # Command arbitration settings
│   ├── my_controllers.yaml # ros2_control configuration
│   └── *.rviz             # RViz visualization configs
├── description/            # Robot description files
│   ├── robot.urdf.xacro   # Main robot description (Create base + sensors)
│   ├── lidar.xacro        # LiDAR sensor configuration
│   ├── camera.xacro       # Camera sensor configuration
│   ├── ros2_control.xacro # Simulation control interface
│   └── gazebo_control.xacro # Alternative control interface
├── launch/                 # Launch files
│   ├── launch_sim.launch.py    # Simulation mode launcher
│   ├── launch_robot.launch.py  # Real robot mode launcher
│   ├── rsp.launch.py          # Robot State Publisher
│   └── joystick.launch.py     # Controller interface
└── worlds/                 # Gazebo world files
    ├── empty.world        # Basic environment
    └── obstacles.world    # Environment with obstacles
```

## 🔄 Integration Details

### Create Robot Base Integration
- **Physical Description**: Reuses `create_base.urdf.xacro` from create_robot package
- **Specifications**: Maintains exact Create 2 physical parameters
- **Sensors**: Adds LiDAR and camera to Create base platform
- **Control**: Mode-dependent (ros2_control vs create_driver)

### Sensor Frame Transformations
```
base_link (Create robot center)
├── laser (0, 0, 0.2) - LiDAR at geometric center, 200mm up
└── camera_link (0.075, 0, 0.1) - Camera 75mm forward, 100mm up
    └── camera_link_optical - Standard camera optical frame
```

### Mode Switching Logic
The `use_sim_time` parameter determines operational mode:
- `true`: Simulation with ros2_control and differential drive plugin
- `false`: Real robot with create_driver topics and hardware interface

This architecture ensures seamless transition between simulation development and real robot deployment while maintaining consistent interfaces and behavior.