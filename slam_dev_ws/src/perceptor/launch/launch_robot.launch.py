"""
Real Robot Launch Configuration - REAL ROBOT MODE

This launch file configures and starts the complete system for running the Perceptor
physical robot with the integrated Create robot base.

REAL ROBOT MODE CHARACTERISTICS:
- Robot Base: Physical Create 2 robot base with create_driver interface
- Control System: create_driver topics (cmd_vel, odom, joint_states from create_driver)
- Sensors: Physical LiDAR (200mm above base_link), Camera (75mm forward, 100mm up)
- Hardware: Real Create robot motors, encoders, and sensors
- Topics: create_driver topics remapped to match Perceptor expectations

This configuration is specifically designed for real hardware operation (not simulation),
with proper timing and dependencies to ensure controllers start in the correct sequence.
The system provides the foundation for robot movement, sensor integration, and higher-level
autonomous behaviors using the Create robot's built-in capabilities.

Key components launched:
1. Robot State Publisher (RSP): Publishes Create robot's kinematic structure
2. Twist Mux: Arbitrates between multiple velocity command sources
3. create_driver Integration: Uses Create robot's native driver topics
4. Topic Remapping: Maps create_driver outputs to Perceptor topic expectations
5. Sensor Integration: LiDAR and camera data from physical sensors

Command-line equivalents:
    # Robot State Publisher
    ros2 launch perceptor rsp.launch.py use_sim_time:=false use_ros2_control:=true

    # Twist Multiplexer
    ros2 run twist_mux twist_mux --ros-args \
        --params-file <path_to_twist_mux.yaml> \
        -r cmd_vel_out:=diff_cont/cmd_vel_unstamped

    # Controller Manager (after 3 second delay)
    ros2 run controller_manager ros2_control_node --ros-args \
        --params-file <path_to_my_controllers.yaml> \
        -p robot_description:="$(ros2 param get /robot_state_publisher robot_description)"

    # Differential Drive Controller (after controller manager starts)
    ros2 run controller_manager spawner diff_cont

    # Joint State Broadcaster (after controller manager starts)
    ros2 run controller_manager spawner joint_broad

Usage:
    ros2 launch perceptor launch_robot.launch.py
"""

import os

from ament_index_python.packages import get_package_share_directory


from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import Command
from launch.actions import RegisterEventHandler
from launch.event_handlers import OnProcessStart

from launch_ros.actions import Node



def generate_launch_description():
    """
    Generate the launch description for the real robot system.

    Returns:
        LaunchDescription: Complete launch configuration for physical robot operation
    """

    # Package name configuration
    # This must match the actual package name for proper resource location
    package_name = 'perceptor'

    # Robot State Publisher (RSP) Launch
    # Includes the robot state publisher configuration with real robot settings
    # Functionality: Publishes Create robot's kinematic structure and transforms
    # Dependencies: Requires robot URDF/xacro files in the description folder
    rsp = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([os.path.join(
            get_package_share_directory(package_name), 'launch', 'rsp.launch.py'
        )]),
        launch_arguments={
            # Real robot mode: Uses system time instead of simulation time
            'use_sim_time': 'false',
            # Disable ros2_control: Use create_driver topics instead
            'use_ros2_control': 'false'
        }.items()
    )

    # Create Robot Driver Launch
    # Includes the create_driver for real robot hardware interface
    # Functionality: Provides cmd_vel, odom, joint_states from Create robot
    # Dependencies: Requires physical Create robot and create_driver package
    create_driver = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([os.path.join(
            get_package_share_directory('create_bringup'), 'launch', 'create_2.launch'
        )]),
        launch_arguments={
            'use_sim_time': 'false'
        }.items()
    )

    # Optional Joystick Control (currently commented out)
    # Uncomment to enable manual joystick control alongside autonomous operation
    # joystick = IncludeLaunchDescription(
    #     PythonLaunchDescriptionSource([os.path.join(
    #         get_package_share_directory(package_name), 'launch', 'joystick.launch.py'
    #     )]), launch_arguments={'use_sim_time': 'false'}.items()
    # )

    # Twist Multiplexer Configuration
    # Path to configuration file defining command source priorities and timeouts
    twist_mux_params = os.path.join(
        get_package_share_directory(package_name),
        'config',
        'twist_mux.yaml'
    )

    # Twist Mux Node - Command arbitration system
    # Manages multiple velocity command sources (joystick, navigation, ball tracker)
    # Functionality: Prioritizes and switches between different control inputs
    # Dependencies: Requires twist_mux.yaml configuration file
    twist_mux = Node(
        package="twist_mux",              # Twist multiplexer package
        executable="twist_mux",           # Main arbitration executable
        parameters=[twist_mux_params],    # Priority and timeout configuration
        remappings=[
            # Output to create_driver cmd_vel topic (real robot mode)
            ('/cmd_vel_out', '/cmd_vel')
        ]
    )



    # Launch Description Assembly
    # All components are launched for real robot operation with create_driver
    return LaunchDescription([
        rsp,                           # Robot state publisher (Create robot model)
        create_driver,                 # Create robot hardware driver
        # joystick,                    # Optional joystick control (commented out)
        twist_mux,                     # Command arbitration (immediate start)
    ])
