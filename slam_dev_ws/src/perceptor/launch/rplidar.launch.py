"""
RPLidar Launch Configuration

This launch file configures and starts the RPLidar laser scanner driver for the
perceptor robot. The RPLidar provides 360-degree laser range measurements
that are essential for navigation, localization, SLAM, and obstacle avoidance.

The RPLidar is a low-cost 2D laser scanner that uses laser triangulation to
measure distances to objects in the environment. It provides high-quality scan
data suitable for robotic applications including mapping, navigation, and
autonomous operation.

Key features:
- 360-degree scanning range
- Configurable scan modes (Standard, Express, Boost)
- Angle compensation for motor speed variations
- USB serial communication interface
- Real-time scan data publishing

Node launched:
- rplidar_composition: RPLidar driver node
  * Publishes: /scan (sensor_msgs/LaserScan) - laser scan measurements
  * Publishes: /tf - laser frame transform (if configured)
  * Provides: Real-time 2D laser range measurements

Command-line equivalent:
    ros2 run rplidar_ros rplidar_composition --ros-args \
        -p serial_port:=/dev/serial/by-path/platform-fd500000.pcie-pci-0000:01:00.0-usb-0:1.3:1.0-port0 \
        -p frame_id:=laser_frame \
        -p angle_compensate:=true \
        -p scan_mode:=Standard

Usage:
    ros2 launch perceptor rplidar.launch.py
"""

import os
from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    """
    Generate the launch description for the RPLidar sensor.

    Returns:
        LaunchDescription: Complete launch configuration for RPLidar operation
    """

    return LaunchDescription([
        # RPLidar Composition Node
        # Driver for RPLidar 2D laser scanner providing 360-degree range measurements
        # Functionality: Interfaces with RPLidar hardware and publishes scan data
        # Dependencies: Requires RPLidar connected via USB and proper device permissions
        Node(
            package='rplidar_ros',           # RPLidar ROS 2 driver package
            executable='rplidar_composition', # Composable RPLidar driver executable
            output='screen',                 # Display driver output in terminal
            parameters=[{
                # Serial Port Configuration
                # Hardware-specific USB device path for RPLidar connection
                # For Pi 5: Use /dev/ttyUSB0 or check actual device path
                # Valid values: /dev/ttyUSB0, /dev/ttyUSB1, or by-path entries
                # Impact: Must match the actual USB connection of the RPLidar
                'serial_port': '/dev/ttyUSB0',

                # Frame ID Configuration
                # TF frame name for the laser scanner coordinate system
                # Must match the frame defined in the robot's URDF description
                # Valid values: Any valid TF frame name
                # Impact: Links laser data to robot's coordinate system for navigation
                'frame_id': 'laser',

                # Angle Compensation
                # Compensates for motor speed variations during scanning
                # Improves scan accuracy by correcting for non-uniform rotation
                # Valid values: true (recommended), false
                # Impact: Better scan quality and measurement accuracy
                'angle_compensate': True,

                # Scan Mode Configuration
                # Determines scanning speed and measurement density
                # Standard: Balanced speed and accuracy for most applications
                # Valid values: 'Standard', 'Express', 'Boost' (model dependent)
                # Impact: Affects scan rate, range, and measurement density
                'scan_mode': 'Standard'
            }]
        )
    ])
