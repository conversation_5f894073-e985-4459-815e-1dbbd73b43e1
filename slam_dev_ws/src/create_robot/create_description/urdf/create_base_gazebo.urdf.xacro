<?xml version="1.0" ?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:macro name="sim_create_base">
    <gazebo>
      <!-- Modern Gazebo Sim Differential Drive Plugin -->
      <plugin name="gz::sim::systems::DiffDrive" filename="gz-sim-diff-drive-system">
        <!-- Wheel Information -->
        <left_joint>left_wheel_joint</left_joint>
        <right_joint>right_wheel_joint</right_joint>
        <wheel_separation>${wheel_separation}</wheel_separation>
        <wheel_radius>${wheel_radius}</wheel_radius>

        <!-- Limits -->
        <max_linear_acceleration>1.0</max_linear_acceleration>
        <max_angular_acceleration>2.0</max_angular_acceleration>

        <!-- Input -->
        <topic>cmd_vel</topic>

        <!-- Output -->
        <frame_id>odom</frame_id>
        <child_frame_id>base_link</child_frame_id>
        <odom_topic>odom</odom_topic>
        <odom_publisher_frequency>30</odom_publisher_frequency>

        <tf_topic>/tf</tf_topic>
      </plugin>

      <!-- Joint State Publisher Plugin -->
      <plugin filename="gz-sim-joint-state-publisher-system"
          name="gz::sim::systems::JointStatePublisher">
          <topic>joint_states</topic>
          <joint_name>left_wheel_joint</joint_name>
          <joint_name>right_wheel_joint</joint_name>
      </plugin>
    </gazebo>

    <gazebo reference="left_wheel_link">
      <mu1>1.0</mu1>
      <mu2>1.0</mu2>
      <kp>1000000.0</kp>
      <kd>100.0</kd>
      <minDepth>0.001</minDepth>
      <maxVel>1.0</maxVel>
    </gazebo>

    <gazebo reference="right_wheel_link">
      <mu1>1.0</mu1>
      <mu2>1.0</mu2>
      <kp>1000000.0</kp>
      <kd>100.0</kd>
      <minDepth>0.001</minDepth>
      <maxVel>1.0</maxVel>
    </gazebo>

    <gazebo reference="front_wheel_link">
      <mu1>0.0</mu1>
      <mu2>0.0</mu2>
      <kp>1000000.0</kp>
      <kd>100.0</kd>
      <minDepth>0.001</minDepth>
      <maxVel>1.0</maxVel>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="sim_imu">
    <gazebo>
      <plugin name="imu_controller" filename="libgazebo_ros_imu.so">
        <alwaysOn>true</alwaysOn>
        <updateRate>30</updateRate>
        <bodyName>gyro_link</bodyName>
        <topicName>imu/data</topicName>
        <serviceName>imu/is_calibrated</serviceName>
        <gaussianNoise>${0.0017*0.0017}</gaussianNoise>
        <xyzOffsets>0 0 0</xyzOffsets>
        <rpyOffsets>0 0 0</rpyOffsets>
      </plugin>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="sim_create_wall_sensor">
    <gazebo reference="wall_sensor_link">
      <sensor type="ray" name="wall_sensor">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.0160</min>
            <max>0.04</max>
            <resolution>0.1</resolution>
          </range>
        </ray>
      </sensor>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="sim_create_cliff_sensors">
    <gazebo reference="left_cliff_sensor_link">
      <sensor type="ray" name="left_cliff_sensor">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.01</min>
            <max>0.04</max>
            <resolution>0.1</resolution>
          </range>
        </ray>
      </sensor>
    </gazebo>

    <gazebo reference="right_cliff_sensor_link">
      <sensor type="ray" name="right_cliff_sensor">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.01</min>
            <max>0.04</max>
            <resolution>0.1</resolution>
          </range>
        </ray>
      </sensor>
    </gazebo>

    <gazebo reference="leftfront_cliff_sensor_link">
      <sensor type="ray" name="leftfront_cliff_sensor">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.01</min>
            <max>0.04</max>
            <resolution>0.1</resolution>
          </range>
        </ray>
      </sensor>
    </gazebo>

    <gazebo reference="rightfront_cliff_sensor_link">
      <sensor type="ray" name="rightfront_cliff_sensor">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.01</min>
            <max>0.04</max>
            <resolution>0.1</resolution>
          </range>
        </ray>
      </sensor>
    </gazebo>
  </xacro:macro>
</robot>
