<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:property name="PI" value="3.1415926535897931" />

  <!-- Material definitions -->
  <material name="Green">
    <color rgba="0.0 1.0 0.0 1.0" />
  </material>

  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0" />
  </material>

  <xacro:macro name="create_wheel" params="prefix y_offset wheel_radius wheel_width">
    <link name="${prefix}_wheel_link">
      <inertial>
        <origin xyz="0 0 0" />
        <mass value="0.01" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>

      <visual>
        <origin xyz="0 0 0" rpy="0 ${PI/2} ${PI/2}" />
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}" />
        </geometry>
      </visual>

      <collision>
        <origin xyz="0 0 0" rpy="0 ${PI/2} ${PI/2}" />
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}" />
        </geometry>
      </collision>
    </link>

    <joint name="${prefix}_wheel_joint" type="continuous">
      <origin xyz="0 ${y_offset} 0.015" rpy="0 0 0" />
      <parent link="base_link" />
      <child link="${prefix}_wheel_link" />
      <axis xyz="0 1 0" />
    </joint>
  </xacro:macro>

  <xacro:macro name="create_base"
    params="diffdrive_update_rate:=40 wheel_separation wheel_radius:=0.036 wheel_width:=0.024 wheel_torque:=1.0 wheel_accel:=1.8 mass_kg:=3.5 base_diameter *mesh">
    <xacro:include filename="$(find create_description)/urdf/create_base_gazebo.urdf.xacro" />

    <link name="base_footprint">
      <visual>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <geometry>
          <box size="0.001 0.001 0.001" />
        </geometry>
        <material name="Green" />
      </visual>
    </link>

    <link name="base_link">
      <inertial>
        <mass value="2" />
        <origin xyz="0 0 0.0" />
        <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.5" />
      </inertial>

      <visual>
        <origin xyz=" 0 0 0.0308" rpy="0 0 0" />
        <geometry>
          <xacro:insert_block name="mesh" />
        </geometry>
      </visual>

      <collision>
        <origin xyz="0.0 0.0 0.0308" rpy="0 0 0" />
        <geometry>
          <cylinder length="0.0611632" radius="0.16495" />
        </geometry>
      </collision>
    </link>

    <link name="wall_sensor_link">
      <inertial>
        <mass value="0.01" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>
    </link>

    <link name="left_cliff_sensor_link">
      <inertial>
        <mass value="0.01" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>
    </link>

    <link name="right_cliff_sensor_link">
      <inertial>
        <mass value="0.01" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>
    </link>

    <link name="leftfront_cliff_sensor_link">
      <inertial>
        <mass value="0.01" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>
    </link>

    <link name="rightfront_cliff_sensor_link">
      <inertial>
        <mass value="0.01" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>
    </link>

    <joint name="base_footprint_joint" type="fixed">
      <origin xyz="0 0 0.017" rpy="0 0 0" />
      <parent link="base_footprint" />
      <child link="base_link" />
    </joint>

    <joint name="base_wall_sensor_joint" type="fixed">
      <origin xyz="0.09 -0.120 0.042" rpy="0 0 -1.0" />
      <parent link="base_link" />
      <child link="wall_sensor_link" />
    </joint>

    <joint name="base_left_cliff_sensor_joint" type="fixed">
      <origin xyz="0.07 0.14 0.01" rpy="0 ${PI/2} 0" />
      <parent link="base_link" />
      <child link="left_cliff_sensor_link" />
    </joint>

    <joint name="base_right_cliff_sensor_joint" type="fixed">
      <origin xyz="0.07 -0.14 0.01" rpy="0 ${PI/2} 0" />
      <parent link="base_link" />
      <child link="right_cliff_sensor_link" />
    </joint>

    <joint name="base_leftfront_cliff_sensor_joint" type="fixed">
      <origin xyz="0.15 0.04 0.01" rpy="0 ${PI/2} 0" />
      <parent link="base_link" />
      <child link="leftfront_cliff_sensor_link" />
    </joint>

    <joint name="base_rightfront_cliff_sensor_joint" type="fixed">
      <origin xyz="0.15 -0.04 0.01" rpy="0 ${PI/2} 0" />
      <parent link="base_link" />
      <child link="rightfront_cliff_sensor_link" />
    </joint>

    <xacro:create_wheel prefix="left" y_offset="${wheel_separation / 2}"
      wheel_radius="${wheel_radius}" wheel_width="${wheel_width}" />
    <xacro:create_wheel prefix="right" y_offset="${wheel_separation / -2}"
      wheel_radius="${wheel_radius}" wheel_width="${wheel_width}" />

    <link name="front_wheel_link">
      <inertial>
        <origin xyz="0 0 0" />
        <mass value="0.01" />
        <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001" />
      </inertial>

      <visual>
        <origin xyz="0 0 0" rpy="0 ${PI/2} ${PI/2}" />
        <geometry>
          <sphere radius="0.018" />
        </geometry>
      </visual>

      <collision>
        <origin xyz="0 0 0" rpy="0 ${PI/2} ${PI/2}" />
        <geometry>
          <sphere radius="0.018" />
        </geometry>
      </collision>
    </link>

    <!-- fixed because there's no transmission -->
    <joint name="front_castor_joint" type="fixed">
      <origin xyz="0.13 0 0.0" rpy="0 0 0" />
      <parent link="base_link" />
      <child link="front_wheel_link" />
      <axis xyz="0 1 0" />
    </joint>

    <joint name="gyro_joint" type="fixed">
      <axis xyz="0 1 0" />
      <origin xyz="0 0 0.04" rpy="0 0 0" />
      <parent link="base_link" />
      <child link="gyro_link" />
    </joint>
    <link name="gyro_link">
      <inertial>
        <mass value="0.001" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.0001" />
      </inertial>
    </link>

    <!-- Simulation sensors -->
    <xacro:sim_create_base />
    <xacro:sim_create_wall_sensor />
    <xacro:sim_create_cliff_sensors />
    <xacro:sim_imu />
  </xacro:macro>
</robot>