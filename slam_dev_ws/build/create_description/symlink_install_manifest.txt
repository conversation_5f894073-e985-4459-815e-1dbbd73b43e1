/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_1.launch
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/create_2.launch
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/launch/roomba_400.launch
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.dae
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_1.tga
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/meshes/create_2.dae
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_1_gazebo.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_2_gazebo.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/urdf/create_base_gazebo.urdf.xacro
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/package_run_dependencies/create_description
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/parent_prefix_path/create_description
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.sh
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/ament_prefix_path.dsv
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.sh
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/environment/path.dsv
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.bash
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.sh
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.zsh
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/local_setup.dsv
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.dsv
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/ament_index/resource_index/packages/create_description
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig.cmake
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/cmake/create_descriptionConfig-version.cmake
/home/<USER>/Roomba/slam_dev_ws/install/create_description/share/create_description/package.xml
