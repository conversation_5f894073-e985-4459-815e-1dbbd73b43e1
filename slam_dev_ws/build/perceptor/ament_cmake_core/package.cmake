set(_AMENT_PACKAGE_NAME "perceptor")
set(perceptor_VERSION "0.0.0")
set(perceptor_MAINTAINER "<PERSON> <<EMAIL>>")
set(perceptor_BUILD_DEPENDS "create_description" "create_bringup" "create_driver" "create_msgs")
set(perceptor_BUILDTOOL_DEPENDS "ament_cmake")
set(perceptor_BUILD_EXPORT_DEPENDS "create_description" "create_bringup" "create_driver" "create_msgs")
set(perceptor_BUILDTOOL_EXPORT_DEPENDS )
set(perceptor_EXEC_DEPENDS "create_description" "create_bringup" "create_driver" "create_msgs")
set(perceptor_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(perceptor_GROUP_DEPENDS )
set(perceptor_MEMBER_OF_GROUPS )
set(perceptor_DEPRECATED "")
set(perceptor_EXPORT_TAGS)
list(APPEND perceptor_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
